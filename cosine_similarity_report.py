#!/usr/bin/env python3
"""
余弦相似度分析报告生成模块
"""

import json
import numpy as np
from datetime import datetime
from typing import Dict, Any


class CosineSimilarityReporter:
    """余弦相似度分析报告生成器"""
    
    def __init__(self, results: Dict[str, Any]):
        self.results = results
        
    def generate_report(self) -> str:
        """生成余弦相似度分析报告"""
        if not self.results:
            return "没有分析结果可生成报告"
            
        results = self.results
        report = []
        
        report.append("# 余弦相似度对案例检索效果影响分析报告\n")
        report.append(f"生成时间: {results['test_config']['timestamp']}\n")
        report.append(f"案例库大小: {results['test_config']['case_library_size']}\n")
        report.append(f"查询案例数: {results['test_config']['query_cases_count']}\n\n")
        
        # 相似度阈值分析
        report.append("## 不同相似度阈值下的检索效果\n\n")
        report.append("| 阈值 | 成功匹配率 | 平均相似度 | 案由匹配率 | 平均质量分数 |\n")
        report.append("|------|------------|------------|------------|-------------|\n")
        
        threshold_analysis = results['threshold_analysis']
        for threshold in sorted(threshold_analysis.keys()):
            data = threshold_analysis[threshold]
            success_rate = data['success_rate'] * 100
            avg_sim = data.get('avg_similarity', 0)
            case_type_rate = data['case_type_match_rate'] * 100
            avg_quality = data.get('avg_quality', 0)
            
            report.append(f"| {threshold} | {success_rate:.1f}% | {avg_sim:.3f} | "
                         f"{case_type_rate:.1f}% | {avg_quality:.3f} |\n")
        
        report.append("\n### 关键发现\n\n")
        
        # 找出最佳阈值
        best_threshold = None
        best_score = 0
        for threshold, data in threshold_analysis.items():
            # 综合评分：成功率 * 0.4 + 案由匹配率 * 0.3 + 质量分数 * 0.3
            score = (data['success_rate'] * 0.4 + 
                    data['case_type_match_rate'] * 0.3 + 
                    data.get('avg_quality', 0) * 0.3)
            if score > best_score:
                best_score = score
                best_threshold = threshold
                
        if best_threshold:
            report.append(f"- **推荐相似度阈值**: {best_threshold}\n")
            best_data = threshold_analysis[best_threshold]
            report.append(f"  - 成功匹配率: {best_data['success_rate']*100:.1f}%\n")
            report.append(f"  - 案由匹配率: {best_data['case_type_match_rate']*100:.1f}%\n")
            report.append(f"  - 平均质量分数: {best_data.get('avg_quality', 0):.3f}\n\n")
        
        # 案由分析
        case_type_analysis = results['case_type_analysis']
        report.append("## 案由对相似度的影响分析\n\n")
        
        report.append("### 案例库中案由分布\n\n")
        case_dist = case_type_analysis['case_type_distribution']
        for case_type, count in sorted(case_dist.items(), key=lambda x: x[1], reverse=True)[:10]:
            report.append(f"- {case_type}: {count} 个案例\n")
        report.append("\n")
        
        # 同案由内相似度分析
        if 'same_type_analysis' in case_type_analysis:
            report.append("### 同案由内证据相似度分析\n\n")
            report.append("| 案由 | 案例数 | 平均相似度 | 标准差 | 最小值 | 最大值 |\n")
            report.append("|------|--------|------------|--------|--------|--------|\n")
            
            same_type = case_type_analysis['same_type_analysis']
            for case_type, data in same_type.items():
                report.append(f"| {case_type} | {data['case_count']} | "
                             f"{data['avg_similarity']:.3f} | {data['std_similarity']:.3f} | "
                             f"{data['min_similarity']:.3f} | {data['max_similarity']:.3f} |\n")
            report.append("\n")
        
        # 跨案由相似度分析
        if 'cross_type_analysis' in case_type_analysis:
            cross_type = case_type_analysis['cross_type_analysis']
            report.append("### 跨案由证据相似度分析\n\n")
            report.append(f"- 平均相似度: {cross_type['avg_similarity']:.3f}\n")
            report.append(f"- 标准差: {cross_type['std_similarity']:.3f}\n")
            report.append(f"- 最小值: {cross_type['min_similarity']:.3f}\n")
            report.append(f"- 最大值: {cross_type['max_similarity']:.3f}\n\n")
        
        # 证据相似度分析
        evidence_analysis = results['evidence_similarity_analysis']
        report.append("## 证据类型相似度分析\n\n")
        report.append(f"- 总证据类型数: {evidence_analysis['total_evidence_types']}\n")
        report.append(f"- 高频证据类型数: {len(evidence_analysis['high_frequency_evidences'])}\n\n")
        
        report.append("### 高频证据类型\n\n")
        high_freq = evidence_analysis['high_frequency_evidences']
        for evidence, count in sorted(high_freq.items(), key=lambda x: x[1], reverse=True)[:10]:
            report.append(f"- {evidence}: {count} 次\n")
        report.append("\n")
        
        # 总结和建议
        report.append("## 总结与建议\n\n")
        report.append("### 主要发现\n\n")
        
        if best_threshold:
            report.append(f"1. **最佳相似度阈值**: {best_threshold}，在此阈值下系统表现最佳\n")
        
        # 分析阈值趋势
        thresholds = sorted(threshold_analysis.keys())
        success_rates = [threshold_analysis[t]['success_rate'] for t in thresholds]
        
        if len(success_rates) >= 2:
            if success_rates[-1] < success_rates[0]:
                report.append("2. **阈值影响**: 随着相似度阈值提高，成功匹配率下降\n")
            else:
                report.append("2. **阈值影响**: 相似度阈值对成功匹配率影响较小\n")
        
        # 案由匹配分析
        if ('same_type_analysis' in case_type_analysis and 
            'cross_type_analysis' in case_type_analysis and
            case_type_analysis['same_type_analysis']):
            
            same_type_avg = np.mean([case_type_analysis['same_type_analysis'][ct]['avg_similarity'] 
                                    for ct in case_type_analysis['same_type_analysis']])
            cross_type_avg = case_type_analysis['cross_type_analysis']['avg_similarity']
            
            report.append(f"3. **案由影响**: 同案由内平均相似度({same_type_avg:.3f})显著高于跨案由({cross_type_avg:.3f})\n")
        
        report.append("\n### 优化建议\n\n")
        if best_threshold:
            report.append(f"1. **设置相似度阈值**: 建议使用{best_threshold}作为检索相似度阈值\n")
        report.append("2. **案由优先策略**: 优先在同案由内进行相似案例检索\n")
        report.append("3. **证据权重优化**: 对高频证据类型进行权重调整\n")
        report.append("4. **多阶段检索**: 先同案由检索，再跨案由补充\n")
        
        return "".join(report)
        
    def generate_json_output(self) -> Dict[str, Any]:
        """生成JSON格式的输出，包含余弦相似度分析结果"""
        if not self.results:
            return {}
            
        # 基础信息
        output = {
            "测试概况": {
                "测试时间": self.results['test_config']['timestamp'],
                "案例库大小": self.results['test_config']['case_library_size'],
                "查询案例数": self.results['test_config']['query_cases_count'],
                "测试相似度阈值": self.results['test_config']['similarity_thresholds']
            }
        }
        
        # 相似度阈值分析结果
        threshold_analysis = self.results['threshold_analysis']
        threshold_results = {}
        
        # 找出最佳阈值
        best_threshold = None
        best_score = 0
        for threshold, data in threshold_analysis.items():
            score = (data['success_rate'] * 0.4 + 
                    data['case_type_match_rate'] * 0.3 + 
                    data.get('avg_quality', 0) * 0.3)
            if score > best_score:
                best_score = score
                best_threshold = threshold
        
        for threshold in sorted(threshold_analysis.keys()):
            data = threshold_analysis[threshold]
            threshold_results[f"阈值_{threshold}"] = {
                "相似度阈值": threshold,
                "成功匹配数": data['successful_matches'],
                "失败匹配数": data['failed_matches'],
                "成功匹配率": f"{data['success_rate']*100:.1f}%",
                "案由匹配率": f"{data['case_type_match_rate']*100:.1f}%",
                "平均相似度": data.get('avg_similarity', 0),
                "相似度标准差": data.get('std_similarity', 0),
                "平均质量分数": data.get('avg_quality', 0),
                "是否推荐阈值": threshold == best_threshold
            }
            
        output["相似度阈值分析"] = threshold_results
        
        # 案由影响分析
        case_type_analysis = self.results['case_type_analysis']
        case_type_results = {
            "案由分布": case_type_analysis['case_type_distribution']
        }
        
        # 同案由内相似度分析
        if 'same_type_analysis' in case_type_analysis:
            same_type_details = {}
            for case_type, data in case_type_analysis['same_type_analysis'].items():
                same_type_details[case_type] = {
                    "案例数量": data['case_count'],
                    "平均相似度": data['avg_similarity'],
                    "相似度标准差": data['std_similarity'],
                    "最小相似度": data['min_similarity'],
                    "最大相似度": data['max_similarity']
                }
            case_type_results["同案由内相似度分析"] = same_type_details
            
        # 跨案由相似度分析
        if 'cross_type_analysis' in case_type_analysis:
            cross_type = case_type_analysis['cross_type_analysis']
            case_type_results["跨案由相似度分析"] = {
                "平均相似度": cross_type['avg_similarity'],
                "相似度标准差": cross_type['std_similarity'],
                "最小相似度": cross_type['min_similarity'],
                "最大相似度": cross_type['max_similarity']
            }
            
        output["案由影响分析"] = case_type_results
        
        # 证据相似度分析
        evidence_analysis = self.results['evidence_similarity_analysis']
        output["证据相似度分析"] = {
            "总证据类型数": evidence_analysis['total_evidence_types'],
            "高频证据类型": evidence_analysis['high_frequency_evidences'],
            "证据相似度矩阵": evidence_analysis['evidence_similarity_matrix']
        }
        
        # 优化建议
        recommendations = {
            "推荐相似度阈值": best_threshold,
            "主要发现": [],
            "优化建议": [
                "案由优先策略：优先在同案由内进行相似案例检索",
                "证据权重优化：对高频证据类型进行权重调整", 
                "多阶段检索：先同案由检索，再跨案由补充"
            ]
        }
        
        if best_threshold:
            best_data = threshold_analysis[best_threshold]
            recommendations["推荐阈值性能"] = {
                "成功匹配率": f"{best_data['success_rate']*100:.1f}%",
                "案由匹配率": f"{best_data['case_type_match_rate']*100:.1f}%",
                "平均质量分数": best_data.get('avg_quality', 0)
            }
            recommendations["主要发现"].append(f"最佳相似度阈值为{best_threshold}，在此阈值下系统表现最佳")
            recommendations["优化建议"].insert(0, f"设置相似度阈值：建议使用{best_threshold}作为检索相似度阈值")
        
        # 分析阈值趋势
        thresholds = sorted(threshold_analysis.keys())
        success_rates = [threshold_analysis[t]['success_rate'] for t in thresholds]
        
        if len(success_rates) >= 2:
            if success_rates[-1] < success_rates[0]:
                recommendations["主要发现"].append("随着相似度阈值提高，成功匹配率下降")
            else:
                recommendations["主要发现"].append("相似度阈值对成功匹配率影响较小")
        
        # 案由匹配分析
        if ('same_type_analysis' in case_type_analysis and 
            'cross_type_analysis' in case_type_analysis and
            case_type_analysis['same_type_analysis']):
            
            same_type_avg = np.mean([case_type_analysis['same_type_analysis'][ct]['avg_similarity'] 
                                    for ct in case_type_analysis['same_type_analysis']])
            cross_type_avg = case_type_analysis['cross_type_analysis']['avg_similarity']
            
            recommendations["主要发现"].append(
                f"同案由内平均相似度({same_type_avg:.3f})显著高于跨案由({cross_type_avg:.3f})"
            )
        
        output["优化建议"] = recommendations
        
        return output
