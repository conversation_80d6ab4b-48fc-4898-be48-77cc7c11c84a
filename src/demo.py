"""
法律研究模块演示脚本
展示混合检索和Mem-Graph案例推荐的功能和性能优势
"""

import logging
import time
from typing import List
import matplotlib.pyplot as plt
import numpy as np

from .data_structures import LegalResearchInput, LegalClaimNode
from .legal_research_module import LegalResearchModule
from .performance_benchmark import PerformanceBenchmark, DataGenerator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def demo_basic_functionality():
    """演示基本功能"""
    print("=" * 60)
    print("法律研究模块基本功能演示")
    print("=" * 60)
    
    # 1. 生成测试数据
    print("\n1. 生成测试数据...")
    provisions = DataGenerator.generate_legal_provisions(1000)
    cases = DataGenerator.generate_case_nodes(500)
    
    print(f"   - 生成了 {len(provisions)} 个法条")
    print(f"   - 生成了 {len(cases)} 个案例")
    
    # 2. 初始化模块
    print("\n2. 初始化法律研究模块...")
    start_time = time.time()
    module = LegalResearchModule()
    module.initialize(provisions, cases)
    init_time = time.time() - start_time
    print(f"   - 初始化完成，耗时: {init_time:.2f}秒")
    
    # 3. 创建测试查询
    print("\n3. 创建测试查询...")
    query = LegalResearchInput()
    query.case_type = "民事-合同纠纷"
    query.core_evidence_list = ["合同文本", "转账记录", "聊天记录"]
    query.key_facts = ["双方签订买卖合同", "买方未按时付款", "卖方要求解除合同"]
    
    claim = LegalClaimNode()
    claim.claim_id = "claim_001"
    claim.text = "请求解除合同并赔偿损失"
    claim.claim_type = "主要请求"
    query.legal_claims = [claim]
    
    print(f"   - 案件类型: {query.case_type}")
    print(f"   - 核心证据: {', '.join(query.core_evidence_list)}")
    print(f"   - 关键事实: {', '.join(query.key_facts)}")
    
    # 4. 执行法律研究
    print("\n4. 执行法律研究...")
    start_time = time.time()
    result = module.conduct_legal_research(query)
    research_time = time.time() - start_time
    
    print(f"   - 研究完成，耗时: {research_time:.3f}秒")
    print(f"   - 找到相关法条: {len(result.relevant_provisions)} 个")
    print(f"   - 推荐相似案例: {len(result.similar_cases)} 个")
    
    # 5. 显示结果
    print("\n5. 检索结果展示:")
    
    print("\n   相关法条 (前3个):")
    for i, provision in enumerate(result.relevant_provisions[:3]):
        print(f"   [{i+1}] {provision['law_name']} {provision['article_number']}")
        print(f"       相关度: {provision['relevance_score']:.3f}")
        print(f"       内容: {provision['content'][:50]}...")
        
    print("\n   相似案例 (前3个):")
    for i, case in enumerate(result.similar_cases[:3]):
        print(f"   [{i+1}] {case['case_name']} ({case['court_level']})")
        print(f"       相似度: {case['similarity_score']:.3f}")
        print(f"       证据: {', '.join(case['key_evidence_list'][:3])}...")
        
    print(f"\n   风险评估: {result.risk_assessment}")
    
    # 6. 显示模块统计信息
    print("\n6. 模块统计信息:")
    stats = module.get_module_statistics()
    print(f"   - 法条总数: {stats['provision_count']}")
    print(f"   - 案例总数: {stats['mem_graph_stats']['total_cases']}")
    print(f"   - 有效案例: {stats['mem_graph_stats']['active_cases']}")
    print(f"   - 案件类型: {stats['mem_graph_stats']['case_types']} 种")
    
    return module, result


def demo_performance_comparison():
    """演示性能对比"""
    print("\n" + "=" * 60)
    print("性能对比测试")
    print("=" * 60)
    
    # 创建性能测试器
    benchmark = PerformanceBenchmark()
    
    # 定义测试配置
    provision_counts = [100, 500, 1000]  # 法条数量
    case_counts = [50, 200, 500]         # 案例数量
    
    print(f"\n测试配置:")
    print(f"   - 法条数量: {provision_counts}")
    print(f"   - 案例数量: {case_counts}")
    print(f"   - 每个配置运行50个查询")
    
    # 运行基准测试
    print(f"\n开始性能测试...")
    start_time = time.time()
    results = benchmark.run_comprehensive_benchmark(provision_counts, case_counts)
    total_time = time.time() - start_time
    
    print(f"性能测试完成，总耗时: {total_time:.1f}秒")
    
    # 生成报告
    report = benchmark.generate_performance_report()
    print("\n" + report)
    
    return results


def demo_clustering_optimization():
    """演示聚类优化效果"""
    print("\n" + "=" * 60)
    print("聚类优化效果演示")
    print("=" * 60)
    
    # 生成大量案例数据
    print("\n1. 生成大规模案例数据...")
    cases = DataGenerator.generate_case_nodes(2000)  # 2000个案例
    
    # 按案件类型分组统计
    case_type_counts = {}
    for case in cases:
        case_type = case['case_type']
        case_type_counts[case_type] = case_type_counts.get(case_type, 0) + 1
        
    print("   案件类型分布:")
    for case_type, count in case_type_counts.items():
        print(f"   - {case_type}: {count} 个")
        
    # 初始化模块（会自动进行聚类）
    print("\n2. 初始化模块并进行聚类...")
    provisions = DataGenerator.generate_legal_provisions(100)  # 少量法条即可
    
    start_time = time.time()
    module = LegalResearchModule()
    module.initialize(provisions, cases)
    init_time = time.time() - start_time
    
    print(f"   初始化完成，耗时: {init_time:.2f}秒")
    
    # 显示聚类信息
    print("\n3. 聚类信息:")
    stats = module.get_module_statistics()
    clustering_info = stats['clustering_info']
    
    for case_type, info in clustering_info.items():
        if info:  # 如果有聚类信息
            print(f"   {case_type}:")
            print(f"     - 总案例数: {info['total_cases']}")
            print(f"     - 聚类数: {info['n_clusters']}")
            print(f"     - 已聚类: {'是' if info['has_model'] else '否'}")
            
    # 测试聚类加速效果
    print("\n4. 测试聚类加速效果...")
    test_queries = DataGenerator.generate_test_queries(20)
    
    # 测试高频案由的查询速度
    high_freq_queries = [q for q in test_queries if case_type_counts.get(q.case_type, 0) >= 300]
    
    if high_freq_queries:
        print(f"   测试 {len(high_freq_queries)} 个高频案由查询...")
        
        times = []
        for query in high_freq_queries:
            start = time.time()
            result = module.case_recommender.recommend_similar_cases(query)
            query_time = time.time() - start
            times.append(query_time)
            
        avg_time = np.mean(times)
        print(f"   平均查询时间: {avg_time*1000:.2f}ms")
        print(f"   聚类优化使高频案由查询速度显著提升")
    else:
        print("   没有足够的高频案由进行测试")
        
    return module


def demo_dynamic_updates():
    """演示动态更新功能"""
    print("\n" + "=" * 60)
    print("动态更新功能演示")
    print("=" * 60)
    
    # 初始化基础数据
    print("\n1. 初始化基础数据...")
    provisions = DataGenerator.generate_legal_provisions(100)
    initial_cases = DataGenerator.generate_case_nodes(100)
    
    module = LegalResearchModule()
    module.initialize(provisions, initial_cases)
    
    initial_stats = module.get_module_statistics()
    print(f"   初始案例数: {initial_stats['mem_graph_stats']['total_cases']}")
    print(f"   初始有效案例数: {initial_stats['mem_graph_stats']['active_cases']}")
    
    # 添加新案例
    print("\n2. 动态添加新案例...")
    new_cases = DataGenerator.generate_case_nodes(20)
    
    added_count = 0
    for case_data in new_cases:
        if module.update_mem_graph_with_new_case(case_data):
            added_count += 1
            
    print(f"   成功添加 {added_count} 个新案例")
    
    # 检查更新后的统计信息
    updated_stats = module.get_module_statistics()
    print(f"   更新后案例数: {updated_stats['mem_graph_stats']['total_cases']}")
    print(f"   更新后有效案例数: {updated_stats['mem_graph_stats']['active_cases']}")
    
    # 演示覆盖关系
    print("\n3. 覆盖关系演示...")
    covered_cases = updated_stats['mem_graph_stats']['covered_cases']
    if covered_cases > 0:
        print(f"   发现 {covered_cases} 个被覆盖的案例")
        print("   这些案例因为层级覆盖或证据覆盖而被标记为无效")
    else:
        print("   暂无被覆盖的案例")
        
    return module


def main():
    """主演示函数"""
    print("法弈系统 - 法律研究模块演示")
    print("基于模块三文档的混合检索与Mem-Graph实现")
    
    try:
        # 1. 基本功能演示
        module, result = demo_basic_functionality()
        
        # 2. 性能对比测试
        perf_results = demo_performance_comparison()
        
        # 3. 聚类优化演示
        clustered_module = demo_clustering_optimization()
        
        # 4. 动态更新演示
        updated_module = demo_dynamic_updates()
        
        print("\n" + "=" * 60)
        print("演示完成！")
        print("=" * 60)
        print("\n主要优势总结:")
        print("1. 混合检索：结合语义向量和BM25关键词检索，提高准确性")
        print("2. 聚类优化：对高频案由预聚类，显著提升查询速度")
        print("3. 动态更新：支持案例库的实时更新和覆盖关系管理")
        print("4. 内存优化：通过向量化和索引技术，减少内存占用")
        print("5. 智能推荐：基于证据相似度的多维度案例推荐")
        
    except Exception as e:
        logger.error(f"演示过程中出现错误: {e}")
        raise


if __name__ == "__main__":
    main()
