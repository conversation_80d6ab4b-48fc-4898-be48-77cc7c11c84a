"""
证据列表向量化与聚类器
实现核心证据列表的向量化和高频案由的预聚类
"""

import numpy as np
from typing import List, Dict
from sklearn.cluster import KMeans
from sklearn.feature_extraction.text import TfidfVectorizer
import jieba
import time
import logging
from .data_structures import CaseNode

logger = logging.getLogger(__name__)


class MockSentenceEncoder:
    """模拟的句子编码器（用于演示）"""
    def __init__(self, dim=384):
        self.dim = dim
        # 使用固定的随机种子确保一致性
        np.random.seed(42)

    def encode(self, text: str) -> np.ndarray:
        """编码文本为向量"""
        # 使用文本哈希值作为种子，确保相同文本产生相同向量
        text_hash = hash(text) % (2**31)
        np.random.seed(text_hash)

        # 生成固定维度的向量
        vector = np.random.normal(0, 0.1, self.dim)

        # 添加一些基于文本内容的特征
        import jieba
        words = list(jieba.cut(text))
        if words:
            # 基于词汇数量调整向量的某些维度
            vector[0] = len(words) / 100.0
            vector[1] = len(text) / 1000.0

        # 归一化
        norm = np.linalg.norm(vector)
        if norm > 0:
            vector = vector / norm

        return vector


class EvidenceVectorizer:
    """证据列表向量化与聚类器"""
    
    def __init__(self):
        self.evidence_encoder = MockSentenceEncoder()
        self.cluster_centroids = {}  # {case_type: [centroid_vector]}
        self.cluster_models = {}     # {case_type: KMeans_model}
        self.case_cluster_mapping = {}  # {case_type: {case_id: cluster_id}}
        
    def vectorize(self, evidence_list: List[str]) -> np.ndarray:
        """将证据列表转换为单一向量"""
        if not evidence_list:
            return np.zeros(self.evidence_encoder.dim)
            
        # 1. 将证据列表排序并拼接
        sorted_evidence = sorted(evidence_list)
        text = " ".join(sorted_evidence)
        
        # 2. 使用模型编码为向量
        return self.evidence_encoder.encode(text)
        
    def pre_cluster_high_frequency_cases(self, case_type: str, cases: List[CaseNode], 
                                       n_clusters: int = 10) -> Dict:
        """对高频案由下的案例进行预聚类"""
        logger.info(f"开始对案由 '{case_type}' 的 {len(cases)} 个案例进行预聚类")
        start_time = time.time()
        
        # 1. 为所有案例生成证据向量
        vectors = []
        case_ids = []
        
        for case in cases:
            if case.evidence_vector is None:
                case.evidence_vector = self.vectorize(case.key_evidence_list)
            vectors.append(case.evidence_vector)
            case_ids.append(case.case_id)
            
        if len(vectors) == 0:
            logger.warning(f"案由 '{case_type}' 没有有效的案例向量")
            return {}
            
        vectors = np.array(vectors)
        
        # 2. 使用K-Means进行聚类
        n_clusters = min(n_clusters, len(cases))  # 确保聚类数不超过案例数
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        cluster_labels = kmeans.fit_predict(vectors)
        
        # 3. 存储聚类结果
        self.cluster_centroids[case_type] = kmeans.cluster_centers_
        self.cluster_models[case_type] = kmeans
        
        # 4. 建立案例到聚类的映射
        case_cluster_map = {}
        for case_id, cluster_id in zip(case_ids, cluster_labels):
            case_cluster_map[case_id] = int(cluster_id)
            
        self.case_cluster_mapping[case_type] = case_cluster_map
        
        cluster_time = time.time() - start_time
        logger.info(f"案由 '{case_type}' 聚类完成，耗时: {cluster_time:.2f}秒，"
                   f"聚类数: {n_clusters}")
        
        # 5. 返回聚类统计信息
        cluster_stats = {}
        for i in range(n_clusters):
            cluster_cases = [case_id for case_id, cid in case_cluster_map.items() if cid == i]
            cluster_stats[f"cluster_{i}"] = {
                'case_count': len(cluster_cases),
                'case_ids': cluster_cases[:5]  # 只返回前5个案例ID作为示例
            }
            
        return {
            'case_type': case_type,
            'total_cases': len(cases),
            'n_clusters': n_clusters,
            'clustering_time': cluster_time,
            'cluster_stats': cluster_stats
        }
        
    def find_closest_clusters(self, case_type: str, query_vector: np.ndarray, 
                            top_k: int = 3) -> List[int]:
        """找到与查询向量最接近的K个聚类中心"""
        if case_type not in self.cluster_centroids:
            return []
            
        centroids = self.cluster_centroids[case_type]
        distances = np.linalg.norm(centroids - query_vector, axis=1)
        closest_indices = np.argsort(distances)[:top_k]
        
        return closest_indices.tolist()
        
    def get_cases_in_clusters(self, case_type: str, cluster_ids: List[int]) -> List[str]:
        """获取指定聚类中的所有案例ID"""
        if case_type not in self.case_cluster_mapping:
            return []
            
        case_cluster_map = self.case_cluster_mapping[case_type]
        case_ids = []
        
        for case_id, cluster_id in case_cluster_map.items():
            if cluster_id in cluster_ids:
                case_ids.append(case_id)
                
        return case_ids
        
    def get_cluster_info(self, case_type: str) -> Dict:
        """获取聚类信息"""
        if case_type not in self.cluster_centroids:
            return {}
            
        return {
            'case_type': case_type,
            'n_clusters': len(self.cluster_centroids[case_type]),
            'total_cases': len(self.case_cluster_mapping.get(case_type, {})),
            'has_model': case_type in self.cluster_models
        }
        
    def calculate_evidence_similarity(self, evidence_list1: List[str], 
                                    evidence_list2: List[str]) -> float:
        """计算两个证据列表的相似度"""
        if not evidence_list1 or not evidence_list2:
            return 0.0
            
        # 使用Jaccard相似度
        set1 = set(evidence_list1)
        set2 = set(evidence_list2)
        
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        if union == 0:
            return 0.0
            
        jaccard_sim = intersection / union
        
        # 结合向量相似度
        vec1 = self.vectorize(evidence_list1)
        vec2 = self.vectorize(evidence_list2)
        
        cosine_sim = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
        
        # 综合相似度 (Jaccard权重更高)
        combined_sim = 0.7 * jaccard_sim + 0.3 * cosine_sim
        
        return float(combined_sim)
