"""
Mem-Graph记忆库实现
基于图结构的案例记忆库，支持动态更新和智能推荐
"""

import numpy as np
from typing import List, Dict, Tuple, Optional
import time
import logging
from datetime import datetime
from .data_structures import CaseNode, MemGraphEdge, OverrideEdge
from .evidence_vectorizer import EvidenceVectorizer

logger = logging.getLogger(__name__)


class MemGraph:
    """Mem-Graph记忆库"""
    
    def __init__(self):
        self.nodes = {}  # node_id -> CaseNode
        self.edges = {}  # edge_id -> MemGraphEdge
        self.case_type_index = {}  # case_type -> List[case_id]
        self.vector_index = {}  # case_type -> {case_id: vector}
        self.vectorizer = EvidenceVectorizer()
        self.high_frequency_threshold = 1000
        
    def add_case(self, case: CaseNode) -> bool:
        """添加案例到图中"""
        try:
            # 生成证据向量
            if case.evidence_vector is None:
                case.evidence_vector = self.vectorizer.vectorize(case.key_evidence_list)
                
            # 添加节点
            self.nodes[case.case_id] = case
            
            # 更新案由索引
            if case.case_type not in self.case_type_index:
                self.case_type_index[case.case_type] = []
            self.case_type_index[case.case_type].append(case.case_id)
            
            # 更新向量索引
            if case.case_type not in self.vector_index:
                self.vector_index[case.case_type] = {}
            self.vector_index[case.case_type][case.case_id] = case.evidence_vector
            
            logger.info(f"成功添加案例: {case.case_id}")
            return True
            
        except Exception as e:
            logger.error(f"添加案例失败: {e}")
            return False
            
    def get_case_count_by_type(self, case_type: str) -> int:
        """获取指定案由的案例数量"""
        return len(self.case_type_index.get(case_type, []))
        
    def get_cases_by_type(self, case_type: str) -> List[CaseNode]:
        """获取指定案由的所有案例"""
        case_ids = self.case_type_index.get(case_type, [])
        return [self.nodes[case_id] for case_id in case_ids if case_id in self.nodes]
        
    def search_by_vector(self, query_vector: np.ndarray, case_type: str = None, 
                        top_k: int = 200) -> List[CaseNode]:
        """基于向量相似度搜索案例"""
        candidates = []
        
        # 确定搜索范围
        if case_type and case_type in self.vector_index:
            search_space = {case_type: self.vector_index[case_type]}
        else:
            search_space = self.vector_index
            
        # 计算相似度
        for ct, case_vectors in search_space.items():
            for case_id, vector in case_vectors.items():
                if case_id in self.nodes:
                    similarity = np.dot(query_vector, vector) / (
                        np.linalg.norm(query_vector) * np.linalg.norm(vector)
                    )
                    candidates.append((self.nodes[case_id], similarity))
                    
        # 排序并返回
        candidates.sort(key=lambda x: x[1], reverse=True)
        return [case for case, _ in candidates[:top_k]]
        
    def get_cases_by_cluster(self, case_type: str, cluster_id: int) -> List[CaseNode]:
        """获取指定聚类中的案例"""
        case_ids = self.vectorizer.get_cases_in_clusters(case_type, [cluster_id])
        return [self.nodes[case_id] for case_id in case_ids if case_id in self.nodes]
        
    def update_case_status(self, case_id: str, status: str) -> bool:
        """更新案例状态"""
        if case_id in self.nodes:
            self.nodes[case_id].effectiveness_status = status
            self.nodes[case_id].updated_time = datetime.now()
            return True
        return False
        
    def add_override_edge(self, source_case_id: str, target_case_id: str, 
                         override_type: str, reason: str) -> bool:
        """添加覆盖关系边"""
        try:
            edge = OverrideEdge()
            edge.edge_id = f"override_{source_case_id}_{target_case_id}"
            edge.source_node_id = source_case_id
            edge.target_node_id = target_case_id
            edge.override_type = override_type
            edge.override_reason = reason
            
            self.edges[edge.edge_id] = edge
            
            # 更新被覆盖案例的状态
            self.update_case_status(target_case_id, "covered")
            
            logger.info(f"添加覆盖关系: {source_case_id} -> {target_case_id} ({override_type})")
            return True
            
        except Exception as e:
            logger.error(f"添加覆盖关系失败: {e}")
            return False
            
    def get_statistics(self) -> Dict:
        """获取图统计信息"""
        total_cases = len(self.nodes)
        active_cases = sum(1 for case in self.nodes.values() 
                          if case.effectiveness_status == "valid")
        covered_cases = total_cases - active_cases
        
        case_type_stats = {}
        for case_type, case_ids in self.case_type_index.items():
            case_type_stats[case_type] = len(case_ids)
            
        return {
            'total_cases': total_cases,
            'active_cases': active_cases,
            'covered_cases': covered_cases,
            'total_edges': len(self.edges),
            'case_types': len(self.case_type_index),
            'case_type_distribution': case_type_stats
        }


class MemGraphCaseRecommender:
    """基于Mem-Graph的案例推荐器"""
    
    def __init__(self, mem_graph: MemGraph):
        self.mem_graph = mem_graph
        self.vectorizer = mem_graph.vectorizer
        self.high_frequency_threshold = 1000
        
    def recommend_similar_cases(self, input_data, top_k: int = 10) -> List[Tuple[CaseNode, float]]:
        """推荐相似案例"""
        start_time = time.time()
        
        # 步骤1: 向量化当前案例的核心证据
        current_vector = self.vectorizer.vectorize(input_data.core_evidence_list)
        
        # 步骤2: 预筛选候选案例
        candidate_cases = self._pre_screen_candidates(input_data.case_type, current_vector)
        
        logger.info(f"预筛选得到 {len(candidate_cases)} 个候选案例")
        
        # 步骤3: 计算深度相似度
        similarity_results = []
        for candidate in candidate_cases:
            score = self._calculate_deep_similarity(input_data, candidate)
            similarity_results.append((candidate, score))
            
        # 步骤4: 过滤被覆盖的案例
        active_cases = self._filter_covered_cases(similarity_results)
        
        # 步骤5: 排序并返回
        active_cases.sort(key=lambda x: x[1], reverse=True)
        
        total_time = time.time() - start_time
        logger.info(f"案例推荐完成，耗时: {total_time:.3f}秒，返回 {min(top_k, len(active_cases))} 个案例")
        
        return active_cases[:top_k]
        
    def _pre_screen_candidates(self, case_type: str, query_vector: np.ndarray) -> List[CaseNode]:
        """预筛选候选案例"""
        case_count = self.mem_graph.get_case_count_by_type(case_type)
        
        # 策略一：高频案由，使用聚类预筛选
        if case_count > self.high_frequency_threshold:
            # 找到最近的聚类中心
            closest_clusters = self.vectorizer.find_closest_clusters(case_type, query_vector, top_k=3)
            
            if closest_clusters:
                candidates = []
                for cluster_id in closest_clusters:
                    cluster_cases = self.mem_graph.get_cases_by_cluster(case_type, cluster_id)
                    candidates.extend(cluster_cases)
                return list(set(candidates))  # 去重
                
        # 策略二：低频案由或聚类失败，使用向量搜索
        return self.mem_graph.search_by_vector(query_vector, case_type, top_k=200)
        
    def _calculate_deep_similarity(self, current_case, candidate: CaseNode) -> float:
        """计算多维度深度相似度"""
        # 1. 证据相似度 (最高权重)
        evidence_sim = self.vectorizer.calculate_evidence_similarity(
            current_case.core_evidence_list,
            candidate.key_evidence_list
        )
        
        # 2. 事实相似度 (简化版本，使用关键词重合度)
        current_facts = " ".join(current_case.key_facts)
        fact_sim = self._calculate_text_similarity(current_facts, candidate.case_facts_summary)
        
        # 3. 法律争议相似度
        current_claims = [claim.text for claim in current_case.legal_claims]
        legal_sim = self._calculate_list_overlap(current_claims, candidate.legal_basis)
        
        # 综合评分
        overall_similarity = (
            0.5 * evidence_sim +   # 证据相似度 (权重最高)
            0.3 * fact_sim +       # 事实相似度
            0.2 * legal_sim        # 法律争议相似度
        )
        
        return overall_similarity
        
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度"""
        if not text1 or not text2:
            return 0.0
            
        # 简单的词汇重合度计算
        import jieba
        words1 = set(jieba.cut(text1))
        words2 = set(jieba.cut(text2))
        
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0
        
    def _calculate_list_overlap(self, list1: List[str], list2: List[str]) -> float:
        """计算列表重合度"""
        if not list1 or not list2:
            return 0.0
            
        set1 = set(list1)
        set2 = set(list2)
        
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        return intersection / union if union > 0 else 0.0
        
    def _filter_covered_cases(self, results: List[Tuple[CaseNode, float]]) -> List[Tuple[CaseNode, float]]:
        """过滤掉被覆盖的案例"""
        return [(case, score) for case, score in results 
                if case.effectiveness_status == "valid"]
