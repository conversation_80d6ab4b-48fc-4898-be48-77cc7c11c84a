"""
性能基准测试
对比混合检索方案与传统方案的时间空间优势
"""

import time
import psutil
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from typing import List, Dict, Tuple
import random
import logging
from datetime import datetime, timedelta

from .data_structures import LegalProvision, CaseNode, LegalResearchInput, LegalClaimNode
from .legal_research_module import LegalResearchModule
from .hybrid_retriever import HybridProvisionRetriever
from .mem_graph import MemGraph, MemGraphCaseRecommender

logger = logging.getLogger(__name__)


class TraditionalRetriever:
    """传统检索方法（仅关键词匹配）"""
    
    def __init__(self):
        self.provisions = []
        self.cases = []
        
    def initialize(self, provisions: List[LegalProvision], cases: List[CaseNode] = None):
        self.provisions = provisions
        self.cases = cases or []
        
    def retrieve_provisions(self, query: str, top_k: int = 10) -> List[dict]:
        """传统关键词匹配检索法条"""
        import jieba
        query_words = set(jieba.cut(query))
        
        scores = []
        for provision in self.provisions:
            content_words = set(jieba.cut(provision.content))
            # 简单的词汇重合度
            overlap = len(query_words.intersection(content_words))
            scores.append((provision, overlap))
            
        scores.sort(key=lambda x: x[1], reverse=True)
        
        results = []
        for provision, score in scores[:top_k]:
            results.append({
                'provision_id': provision.provision_id,
                'law_name': provision.law_name,
                'article_number': provision.article_number,
                'content': provision.content,
                'relevance_score': score
            })
            
        return results
        
    def retrieve_cases(self, evidence_list: List[str], case_type: str, top_k: int = 10) -> List[dict]:
        """传统案例检索（简单相似度）"""
        scores = []
        evidence_set = set(evidence_list)
        
        for case in self.cases:
            if case.case_type != case_type:
                continue
                
            case_evidence_set = set(case.key_evidence_list)
            # 简单的Jaccard相似度
            intersection = len(evidence_set.intersection(case_evidence_set))
            union = len(evidence_set.union(case_evidence_set))
            similarity = intersection / union if union > 0 else 0
            
            scores.append((case, similarity))
            
        scores.sort(key=lambda x: x[1], reverse=True)
        
        results = []
        for case, score in scores[:top_k]:
            results.append({
                'case_id': case.case_id,
                'case_name': case.case_name,
                'similarity_score': score
            })
            
        return results


class DataGenerator:
    """测试数据生成器"""
    
    @staticmethod
    def generate_legal_provisions(count: int) -> List[LegalProvision]:
        """生成模拟法条数据"""
        provisions = []
        law_names = ["民法典", "刑法", "行政法", "商法", "劳动法"]
        
        for i in range(count):
            provision = LegalProvision()
            provision.provision_id = f"provision_{i:06d}"
            provision.law_name = random.choice(law_names)
            provision.article_number = f"第{i%1000+1}条"
            provision.content = f"法条内容{i}：关于{random.choice(['合同', '侵权', '物权', '债权', '继承'])}的规定，" \
                              f"涉及{random.choice(['责任', '义务', '权利', '程序', '标准'])}等方面的具体条款。"
            provisions.append(provision)
            
        return provisions
        
    @staticmethod
    def generate_case_nodes(count: int) -> List[Dict]:
        """生成模拟案例数据"""
        cases = []
        case_types = ["民事-合同纠纷", "民事-侵权纠纷", "刑事-盗窃", "行政-行政处罚", "商事-公司纠纷"]
        court_levels = ["基层", "中院", "高院", "最高院"]
        evidence_types = ["合同文本", "转账记录", "聊天记录", "证人证言", "鉴定报告", 
                         "发票凭证", "录音录像", "现场照片", "专家意见", "公证书"]
        
        for i in range(count):
            case_data = {
                'case_id': f"case_{i:06d}",
                'case_name': f"案例{i}",
                'court_name': f"某地{random.choice(['人民法院', '中级人民法院', '高级人民法院'])}",
                'court_level': random.choice(court_levels),
                'case_type': random.choice(case_types),
                'judgment_date': datetime.now() - timedelta(days=random.randint(1, 3650)),
                'case_facts_summary': f"案例{i}的事实摘要，涉及相关法律问题和争议焦点。",
                'court_opinion': f"法院认为案例{i}符合相关法律规定。",
                'judgment_result': random.choice(["支持原告诉讼请求", "驳回原告诉讼请求", "部分支持"]),
                'legal_basis': [f"provision_{random.randint(0, 999):06d}" for _ in range(random.randint(1, 5))],
                'key_evidence_list': random.sample(evidence_types, random.randint(2, 6))
            }
            cases.append(case_data)
            
        return cases
        
    @staticmethod
    def generate_test_queries(count: int) -> List[LegalResearchInput]:
        """生成测试查询"""
        queries = []
        case_types = ["民事-合同纠纷", "民事-侵权纠纷", "刑事-盗窃", "行政-行政处罚", "商事-公司纠纷"]
        evidence_types = ["合同文本", "转账记录", "聊天记录", "证人证言", "鉴定报告"]
        
        for i in range(count):
            query = LegalResearchInput()
            query.case_type = random.choice(case_types)
            query.core_evidence_list = random.sample(evidence_types, random.randint(2, 4))
            query.key_facts = [f"关键事实{j}" for j in range(random.randint(2, 5))]
            
            # 生成诉讼请求
            claim = LegalClaimNode()
            claim.claim_id = f"claim_{i}"
            claim.text = f"请求{i}"
            claim.claim_type = "主要请求"
            query.legal_claims = [claim]
            
            queries.append(query)
            
        return queries


class PerformanceBenchmark:
    """性能基准测试"""

    def __init__(self):
        self.results = {}
        self.accuracy_results = None
        
    def run_comprehensive_benchmark(self, provision_counts: List[int], case_counts: List[int]):
        """运行综合性能测试"""
        logger.info("开始运行综合性能基准测试")
        
        results = {
            'provision_retrieval': {},
            'case_recommendation': {},
            'memory_usage': {},
            'initialization_time': {}
        }
        
        for prov_count in provision_counts:
            for case_count in case_counts:
                logger.info(f"测试配置: {prov_count} 法条, {case_count} 案例")
                
                # 生成测试数据
                provisions = DataGenerator.generate_legal_provisions(prov_count)
                cases = DataGenerator.generate_case_nodes(case_count)
                queries = DataGenerator.generate_test_queries(50)  # 50个测试查询
                
                # 测试混合检索方案
                hybrid_results = self._test_hybrid_approach(provisions, cases, queries)
                
                # 测试传统方案
                traditional_results = self._test_traditional_approach(provisions, cases, queries)
                
                # 记录结果
                config_key = f"{prov_count}p_{case_count}c"
                results['provision_retrieval'][config_key] = {
                    'hybrid': hybrid_results['provision_time'],
                    'traditional': traditional_results['provision_time']
                }
                results['case_recommendation'][config_key] = {
                    'hybrid': hybrid_results['case_time'],
                    'traditional': traditional_results['case_time']
                }
                results['memory_usage'][config_key] = {
                    'hybrid': hybrid_results['memory_usage'],
                    'traditional': traditional_results['memory_usage']
                }
                results['initialization_time'][config_key] = {
                    'hybrid': hybrid_results['init_time'],
                    'traditional': traditional_results['init_time']
                }
                
        self.results = results
        return results
        
    def _test_hybrid_approach(self, provisions: List[LegalProvision], 
                            cases: List[Dict], queries: List[LegalResearchInput]) -> Dict:
        """测试混合检索方案"""
        # 记录初始内存
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        # 初始化
        start_time = time.time()
        module = LegalResearchModule()
        module.initialize(provisions, cases)
        init_time = time.time() - start_time
        
        # 记录初始化后内存
        after_init_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        # 执行查询
        provision_times = []
        case_times = []
        
        for query in queries:
            # 法条检索
            start = time.time()
            provisions_result = module.provision_retriever.retrieve(query)
            provision_times.append(time.time() - start)
            
            # 案例推荐
            start = time.time()
            cases_result = module.case_recommender.recommend_similar_cases(query)
            case_times.append(time.time() - start)
            
        return {
            'init_time': init_time,
            'provision_time': np.mean(provision_times),
            'case_time': np.mean(case_times),
            'memory_usage': after_init_memory - initial_memory
        }
        
    def _test_traditional_approach(self, provisions: List[LegalProvision], 
                                 cases: List[Dict], queries: List[LegalResearchInput]) -> Dict:
        """测试传统检索方案"""
        # 记录初始内存
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        # 转换案例数据
        case_nodes = []
        for case_data in cases:
            case = CaseNode()
            case.case_id = case_data['case_id']
            case.case_name = case_data['case_name']
            case.case_type = case_data['case_type']
            case.key_evidence_list = case_data['key_evidence_list']
            case_nodes.append(case)
        
        # 初始化
        start_time = time.time()
        retriever = TraditionalRetriever()
        retriever.initialize(provisions, case_nodes)
        init_time = time.time() - start_time
        
        # 记录初始化后内存
        after_init_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        # 执行查询
        provision_times = []
        case_times = []
        
        for query in queries:
            query_text = " ".join(query.key_facts + [claim.text for claim in query.legal_claims])
            
            # 法条检索
            start = time.time()
            provisions_result = retriever.retrieve_provisions(query_text)
            provision_times.append(time.time() - start)
            
            # 案例检索
            start = time.time()
            cases_result = retriever.retrieve_cases(query.core_evidence_list, query.case_type)
            case_times.append(time.time() - start)
            
        return {
            'init_time': init_time,
            'provision_time': np.mean(provision_times),
            'case_time': np.mean(case_times),
            'memory_usage': after_init_memory - initial_memory
        }
        
    def evaluate_retrieval_accuracy(self, provisions: List, queries: List, ground_truth: Dict) -> Dict:
        """评估检索准确率"""
        from .legal_research_module import LegalResearchModule

        # 初始化混合检索系统
        hybrid_module = LegalResearchModule()
        hybrid_module.initialize(provisions)

        # 初始化传统检索系统
        traditional_retriever = TraditionalRetriever()
        traditional_retriever.initialize(provisions)

        hybrid_scores = []
        traditional_scores = []

        for i, query in enumerate(queries):
            if i not in ground_truth:
                continue

            relevant_ids = set(ground_truth[i])

            # 混合检索结果
            hybrid_results = hybrid_module.provision_retriever.retrieve(query, top_k=10)
            hybrid_retrieved = set([r['provision_id'] for r in hybrid_results])

            # 传统检索结果
            query_text = " ".join(query.key_facts + [claim.text for claim in query.legal_claims])
            traditional_results = traditional_retriever.retrieve_provisions(query_text, top_k=10)
            traditional_retrieved = set([r['provision_id'] for r in traditional_results])

            # 计算精确率和召回率
            hybrid_precision = len(hybrid_retrieved.intersection(relevant_ids)) / len(hybrid_retrieved) if hybrid_retrieved else 0
            hybrid_recall = len(hybrid_retrieved.intersection(relevant_ids)) / len(relevant_ids) if relevant_ids else 0
            hybrid_f1 = 2 * hybrid_precision * hybrid_recall / (hybrid_precision + hybrid_recall) if (hybrid_precision + hybrid_recall) > 0 else 0

            traditional_precision = len(traditional_retrieved.intersection(relevant_ids)) / len(traditional_retrieved) if traditional_retrieved else 0
            traditional_recall = len(traditional_retrieved.intersection(relevant_ids)) / len(relevant_ids) if relevant_ids else 0
            traditional_f1 = 2 * traditional_precision * traditional_recall / (traditional_precision + traditional_recall) if (traditional_precision + traditional_recall) > 0 else 0

            hybrid_scores.append({'precision': hybrid_precision, 'recall': hybrid_recall, 'f1': hybrid_f1})
            traditional_scores.append({'precision': traditional_precision, 'recall': traditional_recall, 'f1': traditional_f1})

        # 计算平均值
        hybrid_avg = {
            'precision': sum(s['precision'] for s in hybrid_scores) / len(hybrid_scores),
            'recall': sum(s['recall'] for s in hybrid_scores) / len(hybrid_scores),
            'f1': sum(s['f1'] for s in hybrid_scores) / len(hybrid_scores)
        }

        traditional_avg = {
            'precision': sum(s['precision'] for s in traditional_scores) / len(traditional_scores),
            'recall': sum(s['recall'] for s in traditional_scores) / len(traditional_scores),
            'f1': sum(s['f1'] for s in traditional_scores) / len(traditional_scores)
        }

        return {
            'hybrid': hybrid_avg,
            'traditional': traditional_avg,
            'improvement': {
                'precision': (hybrid_avg['precision'] - traditional_avg['precision']) / traditional_avg['precision'] * 100 if traditional_avg['precision'] > 0 else 0,
                'recall': (hybrid_avg['recall'] - traditional_avg['recall']) / traditional_avg['recall'] * 100 if traditional_avg['recall'] > 0 else 0,
                'f1': (hybrid_avg['f1'] - traditional_avg['f1']) / traditional_avg['f1'] * 100 if traditional_avg['f1'] > 0 else 0
            }
        }

    def generate_performance_report(self) -> str:
        """生成性能报告"""
        if not self.results:
            return "没有测试结果"

        report = ["# 法律研究模块性能测试报告\n"]

        # 法条检索性能对比
        report.append("## 法条检索性能对比\n")
        report.append("| 配置 | 混合检索(ms) | 传统检索(ms) | 性能提升 |\n")
        report.append("|------|-------------|-------------|----------|\n")

        for config, times in self.results['provision_retrieval'].items():
            hybrid_time = times['hybrid'] * 1000
            traditional_time = times['traditional'] * 1000
            improvement = (traditional_time - hybrid_time) / traditional_time * 100
            report.append(f"| {config} | {hybrid_time:.2f} | {traditional_time:.2f} | {improvement:.1f}% |\n")

        # 案例推荐性能对比
        report.append("\n## 案例推荐性能对比\n")
        report.append("| 配置 | 混合检索(ms) | 传统检索(ms) | 性能提升 |\n")
        report.append("|------|-------------|-------------|----------|\n")

        for config, times in self.results['case_recommendation'].items():
            hybrid_time = times['hybrid'] * 1000
            traditional_time = times['traditional'] * 1000
            improvement = (traditional_time - hybrid_time) / traditional_time * 100
            report.append(f"| {config} | {hybrid_time:.2f} | {traditional_time:.2f} | {improvement:.1f}% |\n")

        # 内存使用对比
        report.append("\n## 内存使用对比\n")
        report.append("| 配置 | 混合检索(MB) | 传统检索(MB) | 内存节省 |\n")
        report.append("|------|-------------|-------------|----------|\n")

        for config, memory in self.results['memory_usage'].items():
            hybrid_mem = memory['hybrid']
            traditional_mem = memory['traditional']
            saving = (traditional_mem - hybrid_mem) / traditional_mem * 100 if traditional_mem > 0 else 0
            report.append(f"| {config} | {hybrid_mem:.1f} | {traditional_mem:.1f} | {saving:.1f}% |\n")

        # 添加准确率对比（如果有的话）
        if hasattr(self, 'accuracy_results'):
            report.append("\n## 检索准确率对比\n")
            report.append("| 指标 | 混合检索 | 传统检索 | 提升幅度 |\n")
            report.append("|------|----------|----------|----------|\n")

            acc = self.accuracy_results
            report.append(f"| 精确率 | {acc['hybrid']['precision']:.3f} | {acc['traditional']['precision']:.3f} | {acc['improvement']['precision']:.1f}% |\n")
            report.append(f"| 召回率 | {acc['hybrid']['recall']:.3f} | {acc['traditional']['recall']:.3f} | {acc['improvement']['recall']:.1f}% |\n")
            report.append(f"| F1分数 | {acc['hybrid']['f1']:.3f} | {acc['traditional']['f1']:.3f} | {acc['improvement']['f1']:.1f}% |\n")

        return "".join(report)
