"""
法律研究模块 - 数据结构定义
根据模块三文档实现的核心数据结构
"""

from typing import List, Dict, Optional
from datetime import datetime
import numpy as np
from dataclasses import dataclass, field


@dataclass
class LegalClaimNode:
    """诉讼请求节点"""
    claim_id: str = ""
    text: str = ""
    claim_type: str = ""


@dataclass
class HLKG:
    """异构法律知识图谱（简化版）"""
    entities: Dict = field(default_factory=dict)
    relations: Dict = field(default_factory=dict)


@dataclass
class LegalResearchInput:
    """法律研究模块输入"""
    hlkg: Optional[HLKG] = None
    core_evidence_list: List[str] = field(default_factory=list)
    case_type: str = ""
    legal_claims: List[LegalClaimNode] = field(default_factory=list)
    key_facts: List[str] = field(default_factory=list)
    jurisdiction: str = ""


@dataclass
class LegalProvision:
    """法律条款（支持向量化）"""
    provision_id: str = ""
    law_name: str = ""
    article_number: str = ""
    content: str = ""
    embedding_vector: Optional[np.ndarray] = None


class MemGraphNode:
    """Mem-Graph节点基类"""
    def __init__(self):
        self.node_id: str = ""
        self.node_type: str = ""
        self.created_time: datetime = datetime.now()
        self.updated_time: datetime = datetime.now()
        self.status: str = "active"


class CaseNode(MemGraphNode):
    """案例节点"""
    def __init__(self):
        super().__init__()
        self.node_type = "case"
        self.case_id: str = ""
        self.case_name: str = ""
        self.court_name: str = ""
        self.court_level: str = ""  # 最高院/高院/中院/基层
        self.case_type: str = ""
        self.judgment_date: Optional[datetime] = None
        self.case_facts_summary: str = ""
        self.court_opinion: str = ""
        self.judgment_result: str = ""
        self.legal_basis: List[str] = []  # 条款ID列表
        self.key_evidence_list: List[str] = []  # 核心证据列表
        self.evidence_vector: Optional[np.ndarray] = None
        self.effectiveness_status: str = "valid"  # valid/covered


class MemGraphEdge:
    """Mem-Graph边（关系）基类"""
    def __init__(self):
        self.edge_id: str = ""
        self.source_node_id: str = ""
        self.target_node_id: str = ""
        self.edge_type: str = ""
        self.weight: float = 1.0
        self.attributes: Dict = {}


class OverrideEdge(MemGraphEdge):
    """覆盖关系：案例A → 案例B (A覆盖B)"""
    def __init__(self):
        super().__init__()
        self.edge_type = "override"
        self.override_type: str = ""  # Hierarchical/Evidentiary
        self.override_reason: str = ""


@dataclass
class LegalResearchOutput:
    """法律研究输出"""
    relevant_provisions: List[dict] = field(default_factory=list)
    similar_cases: List[dict] = field(default_factory=list)
    precedent_trends: Dict = field(default_factory=dict)
    risk_assessment: Dict = field(default_factory=dict)
