"""
法律研究模块主接口
整合混合检索和Mem-Graph案例推荐功能
"""

import time
import logging
from typing import Dict, List
from .data_structures import LegalResearchInput, LegalResearchOutput, LegalProvision, CaseNode
from .hybrid_retriever import HybridProvisionRetriever
from .mem_graph import MemGraph, MemGraphCaseRecommender
from .evidence_vectorizer import EvidenceVectorizer

logger = logging.getLogger(__name__)


class MemGraphManager:
    """Mem-Graph动态管理器"""
    
    def __init__(self, mem_graph: MemGraph):
        self.mem_graph = mem_graph
        self.vectorizer = mem_graph.vectorizer
        
    def add_case(self, case_data: Dict) -> bool:
        """添加新案例到Mem-Graph"""
        try:
            # 创建案例节点
            case = CaseNode()
            case.case_id = case_data.get('case_id', '')
            case.case_name = case_data.get('case_name', '')
            case.court_name = case_data.get('court_name', '')
            case.court_level = case_data.get('court_level', '')
            case.case_type = case_data.get('case_type', '')
            case.case_facts_summary = case_data.get('case_facts_summary', '')
            case.court_opinion = case_data.get('court_opinion', '')
            case.judgment_result = case_data.get('judgment_result', '')
            case.legal_basis = case_data.get('legal_basis', [])
            case.key_evidence_list = case_data.get('key_evidence_list', [])
            
            if case_data.get('judgment_date'):
                case.judgment_date = case_data['judgment_date']
                
            # 添加到图中
            success = self.mem_graph.add_case(case)
            
            if success:
                # 检查是否需要建立覆盖关系
                self._check_and_mark_override_relations(case)
                
            return success
            
        except Exception as e:
            logger.error(f"添加案例失败: {e}")
            return False
            
    def _check_and_mark_override_relations(self, new_case: CaseNode):
        """检查并标记覆盖关系"""
        # 获取同类型的相似案例
        similar_cases = self.mem_graph.search_by_vector(
            new_case.evidence_vector, 
            new_case.case_type, 
            top_k=50
        )
        
        for old_case in similar_cases:
            if old_case.case_id == new_case.case_id:
                continue
                
            # 检查层级覆盖
            if self._check_hierarchical_override(new_case, old_case):
                self.mem_graph.add_override_edge(
                    new_case.case_id, 
                    old_case.case_id,
                    "Hierarchical",
                    f"高级法院 {new_case.court_level} 覆盖 {old_case.court_level}"
                )
                
            # 检查证据覆盖
            elif self._check_evidentiary_override(new_case, old_case):
                self.mem_graph.add_override_edge(
                    new_case.case_id,
                    old_case.case_id,
                    "Evidentiary", 
                    "证据更完整，覆盖旧案例"
                )
                
    def _check_hierarchical_override(self, new_case: CaseNode, old_case: CaseNode) -> bool:
        """检查层级覆盖"""
        court_hierarchy = {"最高院": 4, "高院": 3, "中院": 2, "基层": 1}
        
        new_level = court_hierarchy.get(new_case.court_level, 0)
        old_level = court_hierarchy.get(old_case.court_level, 0)
        
        # 新案例法院层级更高，且判决时间更晚
        if (new_level > old_level and 
            new_case.judgment_date and old_case.judgment_date and
            new_case.judgment_date > old_case.judgment_date):
            return True
            
        return False
        
    def _check_evidentiary_override(self, new_case: CaseNode, old_case: CaseNode) -> bool:
        """检查证据覆盖"""
        # 同级法院
        if new_case.court_level != old_case.court_level:
            return False
            
        # 新案例时间更晚
        if (not new_case.judgment_date or not old_case.judgment_date or
            new_case.judgment_date <= old_case.judgment_date):
            return False
            
        # 证据覆盖性检查
        evidence_sim = self.vectorizer.calculate_evidence_similarity(
            new_case.key_evidence_list,
            old_case.key_evidence_list
        )
        
        # 新案例证据更多且相似度高
        if (evidence_sim > 0.9 and 
            len(new_case.key_evidence_list) > len(old_case.key_evidence_list)):
            return True
            
        return False


class LegalResearchModule:
    """法律研究模块主接口"""
    
    def __init__(self):
        self.provision_retriever = HybridProvisionRetriever()
        self.mem_graph = MemGraph()
        self.case_recommender = MemGraphCaseRecommender(self.mem_graph)
        self.mem_graph_manager = MemGraphManager(self.mem_graph)
        self.is_initialized = False
        
    def initialize(self, provisions: List[LegalProvision], cases: List[Dict] = None):
        """初始化模块"""
        logger.info("开始初始化法律研究模块")
        start_time = time.time()
        
        # 初始化法条检索器
        self.provision_retriever.initialize(provisions)
        
        # 初始化案例库
        if cases:
            logger.info(f"加载 {len(cases)} 个案例到Mem-Graph")
            for case_data in cases:
                self.mem_graph_manager.add_case(case_data)
                
            # 对高频案由进行预聚类
            self._perform_pre_clustering()
            
        self.is_initialized = True
        init_time = time.time() - start_time
        logger.info(f"法律研究模块初始化完成，耗时: {init_time:.2f}秒")
        
    def _perform_pre_clustering(self):
        """对高频案由进行预聚类"""
        logger.info("开始对高频案由进行预聚类")
        
        stats = self.mem_graph.get_statistics()
        case_type_distribution = stats['case_type_distribution']
        
        for case_type, count in case_type_distribution.items():
            if count >= self.mem_graph.high_frequency_threshold:
                cases = self.mem_graph.get_cases_by_type(case_type)
                cluster_result = self.mem_graph.vectorizer.pre_cluster_high_frequency_cases(
                    case_type, cases
                )
                logger.info(f"案由 '{case_type}' 聚类完成: {cluster_result}")
                
    def conduct_legal_research(self, input_data: LegalResearchInput) -> LegalResearchOutput:
        """进行综合法律研究"""
        if not self.is_initialized:
            raise ValueError("模块未初始化")
            
        logger.info("开始进行法律研究")
        start_time = time.time()
        
        output = LegalResearchOutput()
        
        # 1. 检索相关法条
        provision_start = time.time()
        output.relevant_provisions = self.provision_retriever.retrieve(input_data)
        provision_time = time.time() - provision_start
        
        # 2. 推荐相似案例
        case_start = time.time()
        similar_cases_with_scores = self.case_recommender.recommend_similar_cases(input_data)
        case_time = time.time() - case_start
        
        # 转换为输出格式
        output.similar_cases = []
        for case, score in similar_cases_with_scores:
            output.similar_cases.append({
                'case_id': case.case_id,
                'case_name': case.case_name,
                'court_name': case.court_name,
                'court_level': case.court_level,
                'case_facts_summary': case.case_facts_summary,
                'judgment_result': case.judgment_result,
                'key_evidence_list': case.key_evidence_list,
                'similarity_score': score
            })
            
        # 3. 生成简单的风险评估
        output.risk_assessment = self._generate_risk_assessment(output.similar_cases)
        
        total_time = time.time() - start_time
        logger.info(f"法律研究完成 - 法条检索: {provision_time:.3f}s, "
                   f"案例推荐: {case_time:.3f}s, 总耗时: {total_time:.3f}s")
        
        return output
        
    def _generate_risk_assessment(self, similar_cases: List[Dict]) -> Dict:
        """生成风险评估"""
        if not similar_cases:
            return {'risk_level': 'unknown', 'confidence': 0.0}
            
        # 简单的风险评估逻辑
        total_score = sum(case['similarity_score'] for case in similar_cases)
        avg_score = total_score / len(similar_cases)
        
        if avg_score > 0.8:
            risk_level = 'low'
        elif avg_score > 0.6:
            risk_level = 'medium'
        else:
            risk_level = 'high'
            
        return {
            'risk_level': risk_level,
            'confidence': avg_score,
            'similar_case_count': len(similar_cases),
            'top_similarity_score': similar_cases[0]['similarity_score'] if similar_cases else 0.0
        }
        
    def update_mem_graph_with_new_case(self, case_data: Dict) -> bool:
        """用新案例动态更新Mem-Graph"""
        return self.mem_graph_manager.add_case(case_data)
        
    def get_module_statistics(self) -> Dict:
        """获取模块统计信息"""
        mem_graph_stats = self.mem_graph.get_statistics()
        
        return {
            'is_initialized': self.is_initialized,
            'provision_count': len(self.provision_retriever.provisions_db),
            'mem_graph_stats': mem_graph_stats,
            'clustering_info': {
                case_type: self.mem_graph.vectorizer.get_cluster_info(case_type)
                for case_type in self.mem_graph.case_type_index.keys()
            }
        }
