"""
法律条款混合检索器
实现语义向量检索与BM25关键词检索的融合
"""

import numpy as np
from typing import List, Dict, Tuple
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import jieba
import time
import logging
from .data_structures import LegalResearchInput, LegalProvision

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MockEmbeddingModel:
    """模拟的嵌入模型（用于演示）"""
    def __init__(self, dim=384):  # 改为384维，与证据向量化器保持一致
        self.dim = dim

    def encode(self, text: str) -> np.ndarray:
        """将文本编码为向量"""
        # 使用文本哈希值作为种子，确保相同文本产生相同向量
        text_hash = hash(text) % (2**31)
        np.random.seed(text_hash)

        # 生成固定维度的向量
        vector = np.random.normal(0, 0.1, self.dim)

        # 添加一些基于文本内容的确定性特征
        words = list(jieba.cut(text))
        if words:
            vector[0] = len(words) / 100.0
            vector[1] = len(text) / 1000.0

        # 归一化
        norm = np.linalg.norm(vector)
        if norm > 0:
            vector = vector / norm

        return vector


class BM25Index:
    """BM25检索索引"""
    def __init__(self, k1=1.5, b=0.75):
        self.k1 = k1
        self.b = b
        self.documents = []
        self.doc_freqs = []
        self.idf = {}
        self.doc_len = []
        self.avgdl = 0
        
    def fit(self, documents: List[str]):
        """构建BM25索引"""
        self.documents = documents
        self.doc_len = []
        
        # 分词并计算文档长度
        tokenized_docs = []
        for doc in documents:
            tokens = list(jieba.cut(doc))
            tokenized_docs.append(tokens)
            self.doc_len.append(len(tokens))
            
        self.avgdl = sum(self.doc_len) / len(self.doc_len)
        
        # 计算词频和IDF
        df = {}
        for tokens in tokenized_docs:
            for token in set(tokens):
                df[token] = df.get(token, 0) + 1
                
        for token, freq in df.items():
            self.idf[token] = np.log((len(documents) - freq + 0.5) / (freq + 0.5))
            
        # 计算每个文档的词频
        self.doc_freqs = []
        for tokens in tokenized_docs:
            doc_freq = {}
            for token in tokens:
                doc_freq[token] = doc_freq.get(token, 0) + 1
            self.doc_freqs.append(doc_freq)
    
    def search(self, query: str, limit: int = 10) -> List[Tuple[int, float]]:
        """BM25搜索"""
        query_tokens = list(jieba.cut(query))
        scores = []
        
        for i, doc_freq in enumerate(self.doc_freqs):
            score = 0
            for token in query_tokens:
                if token in doc_freq:
                    tf = doc_freq[token]
                    idf = self.idf.get(token, 0)
                    score += idf * (tf * (self.k1 + 1)) / (
                        tf + self.k1 * (1 - self.b + self.b * self.doc_len[i] / self.avgdl)
                    )
            scores.append((i, score))
            
        scores.sort(key=lambda x: x[1], reverse=True)
        return scores[:limit]


class VectorIndex:
    """向量索引（简化版FAISS）"""
    def __init__(self):
        self.vectors = []
        self.ids = []
        
    def add(self, vectors: np.ndarray, ids: List[str]):
        """添加向量到索引"""
        if len(self.vectors) == 0:
            self.vectors = vectors
        else:
            self.vectors = np.vstack([self.vectors, vectors])
        self.ids.extend(ids)
        
    def search(self, query_vector: np.ndarray, k: int = 10) -> List[Tuple[str, float]]:
        """向量相似度搜索"""
        if len(self.vectors) == 0:
            return []
            
        similarities = cosine_similarity([query_vector], self.vectors)[0]
        top_indices = np.argsort(similarities)[::-1][:k]
        
        results = []
        for idx in top_indices:
            results.append((self.ids[idx], similarities[idx]))
        return results


class HybridProvisionRetriever:
    """法律条款混合检索器"""
    
    def __init__(self):
        self.embedding_model = MockEmbeddingModel()
        self.vector_index = VectorIndex()
        self.bm25_index = BM25Index()
        self.provisions_db = {}  # provision_id -> LegalProvision
        self.is_initialized = False
        
    def initialize(self, provisions: List[LegalProvision]):
        """初始化检索器"""
        logger.info(f"初始化混合检索器，法条数量: {len(provisions)}")
        start_time = time.time()
        
        # 存储法条到数据库
        documents = []
        vectors = []
        ids = []
        
        for provision in provisions:
            self.provisions_db[provision.provision_id] = provision
            documents.append(provision.content)
            ids.append(provision.provision_id)
            
            # 生成向量
            if provision.embedding_vector is None:
                provision.embedding_vector = self.embedding_model.encode(provision.content)
            vectors.append(provision.embedding_vector)
            
        # 构建索引
        self.vector_index.add(np.array(vectors), ids)
        self.bm25_index.fit(documents)
        
        self.is_initialized = True
        init_time = time.time() - start_time
        logger.info(f"检索器初始化完成，耗时: {init_time:.2f}秒")
        
    def retrieve(self, input_data: LegalResearchInput, top_k: int = 10) -> List[dict]:
        """执行混合检索"""
        if not self.is_initialized:
            raise ValueError("检索器未初始化")
            
        start_time = time.time()
        
        # 步骤1: 构建查询
        query_parts = []
        query_parts.extend(input_data.key_facts)
        query_parts.extend([claim.text for claim in input_data.legal_claims])
        query_text = " ".join(query_parts)
        
        logger.info(f"执行混合检索，查询: {query_text[:100]}...")
        
        # 步骤2a: 向量检索
        vector_start = time.time()
        query_vector = self.embedding_model.encode(query_text)
        vector_results = self.vector_index.search(query_vector, k=top_k*2)
        vector_time = time.time() - vector_start
        
        # 步骤2b: BM25检索
        bm25_start = time.time()
        bm25_results = self.bm25_index.search(query_text, limit=top_k*2)
        bm25_time = time.time() - bm25_start
        
        # 步骤3: 结果融合 (Reciprocal Rank Fusion)
        fusion_start = time.time()
        fused_results = self._reciprocal_rank_fusion(vector_results, bm25_results, top_k)
        fusion_time = time.time() - fusion_start
        
        total_time = time.time() - start_time
        
        logger.info(f"检索完成 - 向量检索: {vector_time:.3f}s, BM25检索: {bm25_time:.3f}s, "
                   f"结果融合: {fusion_time:.3f}s, 总耗时: {total_time:.3f}s")
        
        return fused_results
        
    def _reciprocal_rank_fusion(self, vector_results: List[Tuple[str, float]], 
                               bm25_results: List[Tuple[int, float]], 
                               top_k: int) -> List[dict]:
        """Reciprocal Rank Fusion算法"""
        rrf_scores = {}
        k = 60  # RRF参数
        
        # 处理向量检索结果
        for rank, (provision_id, score) in enumerate(vector_results):
            rrf_scores[provision_id] = rrf_scores.get(provision_id, 0) + 1 / (k + rank + 1)
            
        # 处理BM25检索结果
        for rank, (doc_idx, score) in enumerate(bm25_results):
            provision_id = list(self.provisions_db.keys())[doc_idx]
            rrf_scores[provision_id] = rrf_scores.get(provision_id, 0) + 1 / (k + rank + 1)
            
        # 排序并返回结果
        sorted_results = sorted(rrf_scores.items(), key=lambda x: x[1], reverse=True)
        
        final_results = []
        for provision_id, score in sorted_results[:top_k]:
            provision = self.provisions_db[provision_id]
            final_results.append({
                'provision_id': provision_id,
                'law_name': provision.law_name,
                'article_number': provision.article_number,
                'content': provision.content,
                'relevance_score': score
            })
            
        return final_results
