"""
基于Elasticsearch的混合检索器
结合ES的全文搜索和向量相似度搜索
"""

import numpy as np
from typing import List, Dict, Tuple, Optional
from elasticsearch import Elasticsearch
from elasticsearch.helpers import bulk
import time
import logging
from .data_structures import LegalResearchInput, LegalProvision

logger = logging.getLogger(__name__)


class ElasticsearchHybridRetriever:
    """基于Elasticsearch的混合检索器"""
    
    def __init__(self, es_client: Elasticsearch, embedding_model=None):
        self.es_client = es_client
        self.embedding_model = embedding_model
        self.index_name = "legal_provisions_hybrid"
        self.is_initialized = False
        
    def initialize(self, provisions: List[LegalProvision]):
        """初始化ES索引和向量数据"""
        logger.info(f"初始化ES混合检索器，法条数量: {len(provisions)}")
        start_time = time.time()
        
        # 创建索引
        self._create_index()
        
        # 批量插入数据
        self._bulk_index_provisions(provisions)
        
        self.is_initialized = True
        init_time = time.time() - start_time
        logger.info(f"ES混合检索器初始化完成，耗时: {init_time:.2f}秒")
        
    def _create_index(self):
        """创建ES索引"""
        # 删除已存在的索引
        try:
            self.es_client.indices.delete(index=self.index_name)
        except:
            pass
            
        # 索引映射配置
        mapping = {
            "mappings": {
                "properties": {
                    "provision_id": {"type": "keyword"},
                    "law_name": {
                        "type": "text", 
                        "analyzer": "ik_max_word",
                        "fields": {
                            "keyword": {"type": "keyword"}
                        }
                    },
                    "article_number": {"type": "keyword"},
                    "content": {
                        "type": "text", 
                        "analyzer": "ik_max_word"
                    },
                    # 向量字段（ES 8.0+支持）
                    "embedding_vector": {
                        "type": "dense_vector",
                        "dims": 384,  # 根据实际向量维度调整
                        "index": True,
                        "similarity": "cosine"
                    }
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0,
                "analysis": {
                    "analyzer": {
                        "ik_max_word": {
                            "type": "ik_max_word"
                        }
                    }
                }
            }
        }
        
        self.es_client.indices.create(index=self.index_name, body=mapping)
        
    def _bulk_index_provisions(self, provisions: List[LegalProvision]):
        """批量索引法条数据"""
        actions = []
        
        for provision in provisions:
            # 生成向量（如果没有的话）
            if provision.embedding_vector is None and self.embedding_model:
                provision.embedding_vector = self.embedding_model.encode(provision.content)
            
            action = {
                "_index": self.index_name,
                "_id": provision.provision_id,
                "_source": {
                    "provision_id": provision.provision_id,
                    "law_name": provision.law_name,
                    "article_number": provision.article_number,
                    "content": provision.content,
                    "embedding_vector": provision.embedding_vector.tolist() if provision.embedding_vector is not None else None
                }
            }
            actions.append(action)
            
        # 批量插入
        bulk(self.es_client, actions)
        self.es_client.indices.refresh(index=self.index_name)
        
    def retrieve(self, input_data: LegalResearchInput, top_k: int = 10) -> List[dict]:
        """执行混合检索"""
        if not self.is_initialized:
            raise ValueError("检索器未初始化")
            
        start_time = time.time()
        
        # 构建查询文本
        query_parts = []
        query_parts.extend(input_data.key_facts)
        query_parts.extend([claim.text for claim in input_data.legal_claims])
        query_text = " ".join(query_parts)
        
        logger.info(f"执行ES混合检索，查询: {query_text[:100]}...")
        
        # 方案1: 如果ES支持向量搜索（ES 8.0+）
        if self.embedding_model and self._supports_vector_search():
            results = self._hybrid_search_with_es_vectors(query_text, top_k)
        else:
            # 方案2: 外部向量搜索 + ES文本搜索
            results = self._hybrid_search_external_vectors(query_text, top_k)
            
        total_time = time.time() - start_time
        logger.info(f"ES混合检索完成，耗时: {total_time:.3f}s")
        
        return results
        
    def _hybrid_search_with_es_vectors(self, query_text: str, top_k: int) -> List[dict]:
        """使用ES原生向量搜索的混合检索"""
        # 生成查询向量
        query_vector = self.embedding_model.encode(query_text)
        
        # 构建混合查询
        query = {
            "query": {
                "bool": {
                    "should": [
                        # 文本搜索部分
                        {
                            "multi_match": {
                                "query": query_text,
                                "fields": ["law_name^2", "content^3"],
                                "type": "best_fields"
                            }
                        },
                        # 向量搜索部分
                        {
                            "script_score": {
                                "query": {"match_all": {}},
                                "script": {
                                    "source": "cosineSimilarity(params.query_vector, 'embedding_vector') + 1.0",
                                    "params": {"query_vector": query_vector.tolist()}
                                }
                            }
                        }
                    ]
                }
            },
            "size": top_k * 2  # 获取更多候选进行重排序
        }
        
        response = self.es_client.search(index=self.index_name, body=query)
        
        # 处理结果
        results = []
        for hit in response['hits']['hits']:
            results.append({
                'provision_id': hit['_source']['provision_id'],
                'law_name': hit['_source']['law_name'],
                'article_number': hit['_source']['article_number'],
                'content': hit['_source']['content'],
                'relevance_score': hit['_score']
            })
            
        return results[:top_k]
        
    def _hybrid_search_external_vectors(self, query_text: str, top_k: int) -> List[dict]:
        """使用外部向量搜索的混合检索"""
        # 1. ES文本搜索
        text_query = {
            "query": {
                "multi_match": {
                    "query": query_text,
                    "fields": ["law_name^2", "content^3"],
                    "type": "best_fields"
                }
            },
            "size": top_k * 2
        }
        
        text_response = self.es_client.search(index=self.index_name, body=text_query)
        text_results = [(hit['_source']['provision_id'], hit['_score']) 
                       for hit in text_response['hits']['hits']]
        
        # 2. 向量搜索（如果有向量模型）
        vector_results = []
        if self.embedding_model:
            query_vector = self.embedding_model.encode(query_text)
            vector_results = self._vector_search_in_es(query_vector, top_k * 2)
        
        # 3. 结果融合
        fused_results = self._reciprocal_rank_fusion(text_results, vector_results, top_k)
        
        # 4. 获取完整信息
        final_results = []
        for provision_id, score in fused_results:
            doc = self.es_client.get(index=self.index_name, id=provision_id)
            source = doc['_source']
            final_results.append({
                'provision_id': provision_id,
                'law_name': source['law_name'],
                'article_number': source['article_number'],
                'content': source['content'],
                'relevance_score': score
            })
            
        return final_results
        
    def _vector_search_in_es(self, query_vector: np.ndarray, k: int) -> List[Tuple[str, float]]:
        """在ES中进行向量搜索"""
        # 获取所有文档的向量进行相似度计算
        query = {
            "query": {"match_all": {}},
            "_source": ["provision_id", "embedding_vector"],
            "size": 10000  # 根据实际数据量调整
        }
        
        response = self.es_client.search(index=self.index_name, body=query)
        
        similarities = []
        for hit in response['hits']['hits']:
            if hit['_source']['embedding_vector']:
                doc_vector = np.array(hit['_source']['embedding_vector'])
                similarity = np.dot(query_vector, doc_vector) / (
                    np.linalg.norm(query_vector) * np.linalg.norm(doc_vector)
                )
                similarities.append((hit['_source']['provision_id'], similarity))
                
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:k]
        
    def _reciprocal_rank_fusion(self, text_results: List[Tuple[str, float]], 
                               vector_results: List[Tuple[str, float]], 
                               top_k: int) -> List[Tuple[str, float]]:
        """Reciprocal Rank Fusion算法"""
        rrf_scores = {}
        k = 60  # RRF参数
        
        # 处理文本搜索结果
        for rank, (provision_id, score) in enumerate(text_results):
            rrf_scores[provision_id] = rrf_scores.get(provision_id, 0) + 1 / (k + rank + 1)
            
        # 处理向量搜索结果
        for rank, (provision_id, score) in enumerate(vector_results):
            rrf_scores[provision_id] = rrf_scores.get(provision_id, 0) + 1 / (k + rank + 1)
            
        # 排序并返回结果
        sorted_results = sorted(rrf_scores.items(), key=lambda x: x[1], reverse=True)
        return sorted_results[:top_k]
        
    def _supports_vector_search(self) -> bool:
        """检查ES是否支持向量搜索"""
        try:
            # 检查ES版本
            info = self.es_client.info()
            version = info['version']['number']
            major_version = int(version.split('.')[0])
            return major_version >= 8
        except:
            return False
            
    def cleanup(self):
        """清理资源"""
        try:
            self.es_client.indices.delete(index=self.index_name)
        except:
            pass
