#!/usr/bin/env python3
"""
准确率评估测试
创建带有标准答案的测试数据，评估混合检索vs传统检索的真实准确率
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.data_structures import LegalResearchInput, LegalClaimNode, LegalProvision
from src.performance_benchmark import PerformanceBenchmark, DataGenerator
import random
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_ground_truth_dataset():
    """创建带有标准答案的测试数据集"""
    print("创建带有标准答案的测试数据集...")
    
    # 创建法条数据
    provisions = []
    
    # 合同相关法条
    contract_provisions = [
        {"id": "contract_001", "law": "民法典", "article": "第464条", "content": "合同是民事主体之间设立、变更、终止民事法律关系的协议", "keywords": ["合同", "协议", "民事主体"]},
        {"id": "contract_002", "law": "民法典", "article": "第465条", "content": "依法成立的合同，受法律保护", "keywords": ["合同", "法律保护", "依法成立"]},
        {"id": "contract_003", "law": "民法典", "article": "第577条", "content": "当事人一方不履行合同义务或者履行合同义务不符合约定的，应当承担违约责任", "keywords": ["违约责任", "合同义务", "不履行"]},
        {"id": "contract_004", "law": "民法典", "article": "第563条", "content": "有下列情形之一的，当事人可以解除合同", "keywords": ["解除合同", "当事人", "情形"]},
        {"id": "contract_005", "law": "民法典", "article": "第584条", "content": "当事人一方不履行合同义务或者履行合同义务不符合约定，造成对方损失的，损失赔偿额应当相当于因违约所造成的损失", "keywords": ["损失赔偿", "违约", "合同义务"]},
    ]
    
    # 侵权相关法条
    tort_provisions = [
        {"id": "tort_001", "law": "民法典", "article": "第1165条", "content": "行为人因过错侵害他人民事权益造成损害的，应当承担侵权责任", "keywords": ["侵权责任", "过错", "民事权益"]},
        {"id": "tort_002", "law": "民法典", "article": "第1179条", "content": "侵害他人造成人身损害的，应当赔偿医疗费、护理费、交通费等为治疗和康复支出的合理费用", "keywords": ["人身损害", "医疗费", "赔偿"]},
        {"id": "tort_003", "law": "民法典", "article": "第1183条", "content": "侵害自然人人身权益造成严重精神损害的，被侵权人有权请求精神损害赔偿", "keywords": ["精神损害", "人身权益", "赔偿"]},
    ]
    
    # 物权相关法条
    property_provisions = [
        {"id": "property_001", "law": "民法典", "article": "第209条", "content": "不动产物权的设立、变更、转让和消灭，经依法登记，发生效力", "keywords": ["不动产", "物权", "登记"]},
        {"id": "property_002", "law": "民法典", "article": "第240条", "content": "所有权人对自己的不动产或者动产，依法享有占有、使用、收益和处分的权利", "keywords": ["所有权", "占有", "使用", "收益", "处分"]},
    ]
    
    # 转换为LegalProvision对象
    all_provision_data = contract_provisions + tort_provisions + property_provisions
    for prov_data in all_provision_data:
        provision = LegalProvision()
        provision.provision_id = prov_data["id"]
        provision.law_name = prov_data["law"]
        provision.article_number = prov_data["article"]
        provision.content = prov_data["content"]
        provisions.append(provision)
    
    # 添加一些干扰法条
    noise_provisions = DataGenerator.generate_legal_provisions(20)
    provisions.extend(noise_provisions)
    
    # 创建测试查询和标准答案
    test_cases = [
        {
            "query": {
                "case_type": "民事-合同纠纷",
                "key_facts": ["双方签订买卖合同", "卖方未按时交货", "买方要求解除合同"],
                "legal_claims": [{"text": "请求解除合同并赔偿损失", "type": "主要请求"}]
            },
            "relevant_provisions": ["contract_001", "contract_003", "contract_004", "contract_005"]
        },
        {
            "query": {
                "case_type": "民事-合同纠纷", 
                "key_facts": ["合同约定交付时间", "一方违约不履行", "造成经济损失"],
                "legal_claims": [{"text": "请求承担违约责任", "type": "主要请求"}]
            },
            "relevant_provisions": ["contract_002", "contract_003", "contract_005"]
        },
        {
            "query": {
                "case_type": "民事-侵权纠纷",
                "key_facts": ["交通事故", "造成人身伤害", "产生医疗费用"],
                "legal_claims": [{"text": "请求赔偿医疗费和精神损失", "type": "主要请求"}]
            },
            "relevant_provisions": ["tort_001", "tort_002", "tort_003"]
        },
        {
            "query": {
                "case_type": "民事-物权纠纷",
                "key_facts": ["房屋所有权争议", "未办理过户登记", "要求确认所有权"],
                "legal_claims": [{"text": "请求确认房屋所有权", "type": "主要请求"}]
            },
            "relevant_provisions": ["property_001", "property_002"]
        },
        {
            "query": {
                "case_type": "民事-合同纠纷",
                "key_facts": ["合同成立", "受法律保护", "依法履行"],
                "legal_claims": [{"text": "确认合同效力", "type": "主要请求"}]
            },
            "relevant_provisions": ["contract_001", "contract_002"]
        }
    ]
    
    # 转换查询格式
    queries = []
    ground_truth = {}
    
    for i, case in enumerate(test_cases):
        query = LegalResearchInput()
        query.case_type = case["query"]["case_type"]
        query.key_facts = case["query"]["key_facts"]
        
        claim = LegalClaimNode()
        claim.text = case["query"]["legal_claims"][0]["text"]
        claim.claim_type = case["query"]["legal_claims"][0]["type"]
        query.legal_claims = [claim]
        
        queries.append(query)
        ground_truth[i] = case["relevant_provisions"]
    
    print(f"创建了 {len(provisions)} 个法条和 {len(queries)} 个测试查询")
    return provisions, queries, ground_truth


def run_accuracy_evaluation():
    """运行准确率评估"""
    print("=" * 60)
    print("法律研究模块准确率评估测试")
    print("=" * 60)
    
    # 创建测试数据
    provisions, queries, ground_truth = create_ground_truth_dataset()
    
    # 创建基准测试器
    benchmark = PerformanceBenchmark()
    
    # 运行准确率评估
    print("\n开始准确率评估...")
    accuracy_results = benchmark.evaluate_retrieval_accuracy(provisions, queries, ground_truth)
    
    # 保存结果到benchmark对象
    benchmark.accuracy_results = accuracy_results
    
    # 显示结果
    print("\n" + "=" * 60)
    print("准确率评估结果")
    print("=" * 60)
    
    print(f"\n混合检索结果:")
    print(f"  精确率: {accuracy_results['hybrid']['precision']:.3f}")
    print(f"  召回率: {accuracy_results['hybrid']['recall']:.3f}")
    print(f"  F1分数: {accuracy_results['hybrid']['f1']:.3f}")
    
    print(f"\n传统检索结果:")
    print(f"  精确率: {accuracy_results['traditional']['precision']:.3f}")
    print(f"  召回率: {accuracy_results['traditional']['recall']:.3f}")
    print(f"  F1分数: {accuracy_results['traditional']['f1']:.3f}")
    
    print(f"\n性能提升:")
    print(f"  精确率提升: {accuracy_results['improvement']['precision']:.1f}%")
    print(f"  召回率提升: {accuracy_results['improvement']['recall']:.1f}%")
    print(f"  F1分数提升: {accuracy_results['improvement']['f1']:.1f}%")
    
    # 生成完整报告
    report = benchmark.generate_performance_report()
    
    # 保存报告
    with open("accuracy_evaluation_report.md", "w", encoding="utf-8") as f:
        f.write(report)
    
    print(f"\n详细报告已保存到: accuracy_evaluation_report.md")
    
    return accuracy_results


def analyze_detailed_results(provisions, queries, ground_truth):
    """详细分析每个查询的结果"""
    from src.legal_research_module import LegalResearchModule
    from src.performance_benchmark import TraditionalRetriever
    
    print("\n" + "=" * 60)
    print("详细结果分析")
    print("=" * 60)
    
    # 初始化系统
    hybrid_module = LegalResearchModule()
    hybrid_module.initialize(provisions)
    
    traditional_retriever = TraditionalRetriever()
    traditional_retriever.initialize(provisions)
    
    for i, query in enumerate(queries):
        if i not in ground_truth:
            continue
            
        print(f"\n查询 {i+1}: {query.case_type}")
        print(f"关键事实: {', '.join(query.key_facts)}")
        print(f"标准答案: {ground_truth[i]}")
        
        # 混合检索结果
        hybrid_results = hybrid_module.provision_retriever.retrieve(query, top_k=5)
        hybrid_ids = [r['provision_id'] for r in hybrid_results]
        print(f"混合检索: {hybrid_ids}")
        
        # 传统检索结果
        query_text = " ".join(query.key_facts + [claim.text for claim in query.legal_claims])
        traditional_results = traditional_retriever.retrieve_provisions(query_text, top_k=5)
        traditional_ids = [r['provision_id'] for r in traditional_results]
        print(f"传统检索: {traditional_ids}")
        
        # 计算命中情况
        relevant_set = set(ground_truth[i])
        hybrid_hits = len(set(hybrid_ids).intersection(relevant_set))
        traditional_hits = len(set(traditional_ids).intersection(relevant_set))
        
        print(f"命中情况: 混合检索 {hybrid_hits}/{len(relevant_set)}, 传统检索 {traditional_hits}/{len(relevant_set)}")


if __name__ == "__main__":
    try:
        # 运行准确率评估
        results = run_accuracy_evaluation()
        
        # 详细分析
        provisions, queries, ground_truth = create_ground_truth_dataset()
        analyze_detailed_results(provisions, queries, ground_truth)
        
        print("\n" + "=" * 60)
        print("✓ 准确率评估完成！")
        print("现在有了真实的数据支撑的准确率对比结果。")
        print("=" * 60)
        
    except Exception as e:
        print(f"✗ 评估过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
