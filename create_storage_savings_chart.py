#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
案例节省存储比例柱状图生成脚本
展示民事和刑事不同案由的存储节省效果
"""

import json
import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_data(filename):
    """加载JSON数据"""
    with open(filename, 'r', encoding='utf-8') as f:
        return json.load(f)

def extract_case_data(data):
    """提取案由数据"""
    civil_cases = []
    criminal_cases = []
    
    # 提取民事案由数据
    for case in data['民事案由聚类效果']['高频案由']:
        civil_cases.append({
            'name': case['案由'],
            'reduction_rate': float(case['减少率'].rstrip('%')),
            'original_count': case['原始数量'],
            'saved_cases': case['节省案例']
        })
    
    # 提取部分中频民事案由
    for case in data['民事案由聚类效果']['中频案由'][:5]:
        civil_cases.append({
            'name': case['案由'],
            'reduction_rate': float(case['减少率'].rstrip('%')),
            'original_count': case['原始数量'],
            'saved_cases': case['节省案例']
        })
    
    # 提取刑事案由数据
    for case in data['刑事案由聚类效果']['高频案由']:
        criminal_cases.append({
            'name': case['案由'],
            'reduction_rate': float(case['减少率'].rstrip('%')),
            'original_count': case['原始数量'],
            'saved_cases': case['节省案例']
        })
    
    # 提取部分中频刑事案由
    for case in data['刑事案由聚类效果']['中频案由'][:6]:
        criminal_cases.append({
            'name': case['案由'],
            'reduction_rate': float(case['减少率'].rstrip('%')),
            'original_count': case['原始数量'],
            'saved_cases': case['节省案例']
        })
    
    return civil_cases, criminal_cases

def create_chart(civil_cases, criminal_cases):
    """创建柱状图"""
    # 设置图形大小
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 14))
    fig.suptitle('民事与刑事案由存储节省比例对比', fontsize=20, fontweight='bold', y=0.95)
    
    # 民事案由图表
    civil_names = [case['name'] for case in civil_cases]
    civil_rates = [case['reduction_rate'] for case in civil_cases]
    civil_counts = [case['original_count'] for case in civil_cases]
    
    # 创建颜色渐变
    colors_civil = plt.cm.Blues(np.linspace(0.4, 0.9, len(civil_names)))
    
    bars1 = ax1.bar(range(len(civil_names)), civil_rates, color=colors_civil, alpha=0.8, edgecolor='navy', linewidth=0.5)
    ax1.set_title('民事案由存储节省比例', fontsize=16, fontweight='bold', pad=20)
    ax1.set_ylabel('节省比例 (%)', fontsize=12)
    ax1.set_ylim(0, 100)
    ax1.grid(axis='y', alpha=0.3, linestyle='--')
    
    # 设置x轴标签
    ax1.set_xticks(range(len(civil_names)))
    ax1.set_xticklabels(civil_names, rotation=45, ha='right', fontsize=10)
    
    # 在柱子上添加数值标签
    for i, (bar, rate, count) in enumerate(zip(bars1, civil_rates, civil_counts)):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{rate:.1f}%\n({count}例)', 
                ha='center', va='bottom', fontsize=9, fontweight='bold')
    
    # 刑事案由图表
    criminal_names = [case['name'] for case in criminal_cases]
    criminal_rates = [case['reduction_rate'] for case in criminal_cases]
    criminal_counts = [case['original_count'] for case in criminal_cases]
    
    # 创建颜色渐变
    colors_criminal = plt.cm.Reds(np.linspace(0.4, 0.9, len(criminal_names)))
    
    bars2 = ax2.bar(range(len(criminal_names)), criminal_rates, color=colors_criminal, alpha=0.8, edgecolor='darkred', linewidth=0.5)
    ax2.set_title('刑事案由存储节省比例', fontsize=16, fontweight='bold', pad=20)
    ax2.set_ylabel('节省比例 (%)', fontsize=12)
    ax2.set_xlabel('案由类型', fontsize=12)
    ax2.set_ylim(0, 100)
    ax2.grid(axis='y', alpha=0.3, linestyle='--')
    
    # 设置x轴标签
    ax2.set_xticks(range(len(criminal_names)))
    ax2.set_xticklabels(criminal_names, rotation=45, ha='right', fontsize=10)
    
    # 在柱子上添加数值标签
    for i, (bar, rate, count) in enumerate(zip(bars2, criminal_rates, criminal_counts)):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{rate:.1f}%\n({count}例)', 
                ha='center', va='bottom', fontsize=9, fontweight='bold')
    
    # 调整布局
    plt.tight_layout()
    plt.subplots_adjust(top=0.92)
    
    # 保存图表
    plt.savefig('案例存储节省比例对比图.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.show()
    
    # 打印统计信息
    print("\n=== 存储节省效果统计 ===")
    print(f"民事案由平均节省比例: {np.mean(civil_rates):.1f}%")
    print(f"刑事案由平均节省比例: {np.mean(criminal_rates):.1f}%")
    print(f"民事案由总原始案例数: {sum(civil_counts)}")
    print(f"刑事案由总原始案例数: {sum(criminal_counts)}")
    print(f"民事案由总节省案例数: {sum([case['saved_cases'] for case in civil_cases])}")
    print(f"刑事案由总节省案例数: {sum([case['saved_cases'] for case in criminal_cases])}")

def main():
    """主函数"""
    try:
        # 加载数据
        data = load_data('cleg_clustering_analysis.json')
        
        # 提取案由数据
        civil_cases, criminal_cases = extract_case_data(data)
        
        # 创建图表
        create_chart(civil_cases, criminal_cases)
        
        print("图表已生成并保存为 '案例存储节省比例对比图.png'")
        
    except FileNotFoundError:
        print("错误: 找不到文件 'cleg_clustering_analysis.json'")
    except Exception as e:
        print(f"生成图表时发生错误: {e}")

if __name__ == "__main__":
    main()
