# CLEG数据基准测试报告
生成时间: 2025-08-21 17:50:20
测试配置: 100 案例, 200 法条, 50 查询

## 检索速度对比

| 检索方法 | 初始化时间(s) | 平均查询时间(s) | QPS | 内存使用(MB) |
|----------|---------------|-----------------|-----|-------------|
| 混合检索 | 0.072 | 0.014 | 73.2 | 0.0 |
| 传统检索 | 0.000 | 0.051 | 19.6 | 0.0 |
| Elasticsearch | 8.212 | 0.031 | 32.3 | 0.0 |

## 检索准确率对比

| 检索方法 | 精确率 | 召回率 | F1分数 |
|----------|--------|--------|--------|
| 混合检索 | 0.016 | 0.035 | 0.022 |
| 传统检索 | 0.018 | 0.059 | 0.027 |
| Elasticsearch | 0.022 | 0.073 | 0.033 |

## 性能提升分析

### 检索速度提升
- 相比传统检索: 73.2%
- 相比Elasticsearch: 55.8%

### 内存使用优化
- 相比传统检索: 0.0%
- 相比Elasticsearch: 0.0%

### 检索准确率提升
- F1分数相比传统检索: -18.7%
- F1分数相比Elasticsearch: -33.5%

