#!/usr/bin/env python3
"""
余弦相似度分析模块
分析当检索不在案例库中的案例时，通过embedding余弦相似度找到相似案例的效果
"""

import sys
import os
import json
import time
import numpy as np
from typing import List, Dict, Tuple, Any
import logging
from datetime import datetime

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.data_structures import LegalResearchInput, LegalClaimNode, CaseNode
from src.legal_research_module import LegalResearchModule
from src.evidence_vectorizer import EvidenceVectorizer
from test_cleg_benchmark import CLEGDataLoader, CLEGBenchmark

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class CosineSimilarityAnalyzer:
    """余弦相似度分析器"""
    
    def __init__(self):
        self.data_loader = CLEGDataLoader()
        self.benchmark = CLEGBenchmark()
        self.vectorizer = EvidenceVectorizer()
        self.results = {}
        
    def analyze_cosine_similarity_impact(self, num_cases: int = 1000, num_queries: int = 100) -> Dict:
        """分析余弦相似度对检索效果的影响"""
        logger.info(f"开始余弦相似度影响分析 - 案例库大小: {num_cases}, 查询数: {num_queries}")
        
        # 加载数据
        civil_cases = self.data_loader.load_civil_cases(limit=num_cases//2)
        criminal_cases = self.data_loader.load_criminal_cases(limit=num_cases//2)
        all_cases = civil_cases + criminal_cases
        
        if len(all_cases) == 0:
            logger.error("无法加载CLEG数据，请检查数据路径")
            return {}
            
        logger.info(f"成功加载 {len(all_cases)} 个案例")
        
        # 转换为CaseNode格式
        case_nodes = []
        for i, case_data in enumerate(all_cases):
            case_node = self.benchmark._convert_cleg_to_case_node(case_data, i)
            case_nodes.append(case_node)
            
        # 分割数据：80%作为案例库，20%作为查询案例
        split_index = int(len(case_nodes) * 0.8)
        case_library = case_nodes[:split_index]
        query_cases = case_nodes[split_index:split_index + num_queries]
        
        logger.info(f"案例库大小: {len(case_library)}, 查询案例数: {len(query_cases)}")
        
        # 初始化法律研究模块
        module = LegalResearchModule()
        provisions = self.benchmark.generate_legal_provisions(count=100)
        
        # 转换案例库为字典格式
        case_library_dict = []
        for case in case_library:
            case_dict = {
                'case_id': case.case_id,
                'case_name': case.case_name,
                'case_type': case.case_type,
                'court_name': case.court_name,
                'court_level': case.court_level,
                'judgment_date': case.judgment_date.isoformat() if case.judgment_date else None,
                'case_facts_summary': case.case_facts_summary,
                'court_opinion': case.court_opinion,
                'judgment_result': case.judgment_result,
                'key_evidence_list': case.key_evidence_list,
                'legal_basis': getattr(case, 'legal_basis', [])
            }
            case_library_dict.append(case_dict)
            
        module.initialize(provisions, case_library_dict)
        
        # 分析不同相似度阈值下的检索效果
        similarity_thresholds = [0.5, 0.6, 0.7, 0.8, 0.9, 0.95]
        threshold_results = {}
        
        for threshold in similarity_thresholds:
            logger.info(f"分析相似度阈值: {threshold}")
            threshold_results[threshold] = self._analyze_threshold_impact(
                module, query_cases, case_library, threshold
            )
            
        # 分析案由匹配对相似度的影响
        case_type_analysis = self._analyze_case_type_impact(module, query_cases, case_library)
        
        # 分析证据相似度分布
        evidence_similarity_analysis = self._analyze_evidence_similarity_distribution(
            query_cases, case_library
        )
        
        # 整合结果
        results = {
            'test_config': {
                'case_library_size': len(case_library),
                'query_cases_count': len(query_cases),
                'similarity_thresholds': similarity_thresholds,
                'timestamp': datetime.now().isoformat()
            },
            'threshold_analysis': threshold_results,
            'case_type_analysis': case_type_analysis,
            'evidence_similarity_analysis': evidence_similarity_analysis
        }
        
        self.results = results
        return results
        
    def _analyze_threshold_impact(self, module: LegalResearchModule, query_cases: List[CaseNode], 
                                 case_library: List[CaseNode], threshold: float) -> Dict:
        """分析特定相似度阈值的影响"""
        
        results = {
            'threshold': threshold,
            'successful_matches': 0,
            'failed_matches': 0,
            'similarity_scores': [],
            'match_quality_scores': [],
            'case_type_matches': 0,
            'cross_type_matches': 0,
            'detailed_results': []
        }
        
        for query_case in query_cases:
            # 转换为查询格式
            research_input = self._convert_case_to_research_input(query_case)
            
            # 执行案例推荐
            similar_cases_with_scores = module.case_recommender.recommend_similar_cases(research_input)
            
            if not similar_cases_with_scores:
                results['failed_matches'] += 1
                continue
                
            # 获取最相似的案例
            best_match, best_score = similar_cases_with_scores[0]
            
            # 计算embedding余弦相似度
            query_vector = self.vectorizer.vectorize(query_case.key_evidence_list)
            match_vector = self.vectorizer.vectorize(best_match.key_evidence_list)
            cosine_sim = np.dot(query_vector, match_vector) / (
                np.linalg.norm(query_vector) * np.linalg.norm(match_vector)
            )
            
            # 判断是否满足阈值
            if cosine_sim >= threshold:
                results['successful_matches'] += 1
                results['similarity_scores'].append(cosine_sim)
                
                # 计算匹配质量分数
                quality_score = self._calculate_match_quality(query_case, best_match)
                results['match_quality_scores'].append(quality_score)
                
                # 统计案由匹配情况
                if query_case.case_type == best_match.case_type:
                    results['case_type_matches'] += 1
                else:
                    results['cross_type_matches'] += 1
                    
                # 记录详细结果
                results['detailed_results'].append({
                    'query_case_id': query_case.case_id,
                    'query_case_type': query_case.case_type,
                    'match_case_id': best_match.case_id,
                    'match_case_type': best_match.case_type,
                    'cosine_similarity': float(cosine_sim),
                    'overall_score': float(best_score),
                    'quality_score': quality_score,
                    'case_type_match': query_case.case_type == best_match.case_type
                })
            else:
                results['failed_matches'] += 1
                
        # 计算统计指标
        if results['similarity_scores']:
            results['avg_similarity'] = float(np.mean(results['similarity_scores']))
            results['std_similarity'] = float(np.std(results['similarity_scores']))
            results['min_similarity'] = float(np.min(results['similarity_scores']))
            results['max_similarity'] = float(np.max(results['similarity_scores']))
            
        if results['match_quality_scores']:
            results['avg_quality'] = float(np.mean(results['match_quality_scores']))
            results['std_quality'] = float(np.std(results['match_quality_scores']))
            
        results['success_rate'] = results['successful_matches'] / len(query_cases)
        results['case_type_match_rate'] = (results['case_type_matches'] / 
                                          max(results['successful_matches'], 1))
        
        return results
        
    def _analyze_case_type_impact(self, module: LegalResearchModule, query_cases: List[CaseNode], 
                                 case_library: List[CaseNode]) -> Dict:
        """分析案由匹配对相似度的影响"""
        
        # 按案由分组
        case_type_groups = {}
        for case in case_library:
            if case.case_type not in case_type_groups:
                case_type_groups[case.case_type] = []
            case_type_groups[case.case_type].append(case)
            
        results = {
            'case_type_distribution': {k: len(v) for k, v in case_type_groups.items()},
            'same_type_analysis': {},
            'cross_type_analysis': {}
        }
        
        # 分析同案由内的相似度分布
        for case_type, cases in case_type_groups.items():
            if len(cases) < 2:
                continue
                
            similarities = []
            for i in range(min(10, len(cases))):  # 取前10个案例进行分析
                for j in range(i+1, min(10, len(cases))):
                    vec1 = self.vectorizer.vectorize(cases[i].key_evidence_list)
                    vec2 = self.vectorizer.vectorize(cases[j].key_evidence_list)
                    sim = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
                    similarities.append(float(sim))
                    
            if similarities:
                results['same_type_analysis'][case_type] = {
                    'case_count': len(cases),
                    'avg_similarity': float(np.mean(similarities)),
                    'std_similarity': float(np.std(similarities)),
                    'min_similarity': float(np.min(similarities)),
                    'max_similarity': float(np.max(similarities))
                }
                
        # 分析跨案由的相似度分布
        cross_type_similarities = []
        case_types = list(case_type_groups.keys())
        
        for i in range(len(case_types)):
            for j in range(i+1, len(case_types)):
                type1_cases = case_type_groups[case_types[i]][:5]  # 取前5个
                type2_cases = case_type_groups[case_types[j]][:5]  # 取前5个
                
                for case1 in type1_cases:
                    for case2 in type2_cases:
                        vec1 = self.vectorizer.vectorize(case1.key_evidence_list)
                        vec2 = self.vectorizer.vectorize(case2.key_evidence_list)
                        sim = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
                        cross_type_similarities.append({
                            'type1': case_types[i],
                            'type2': case_types[j],
                            'similarity': float(sim)
                        })
                        
        if cross_type_similarities:
            sims = [item['similarity'] for item in cross_type_similarities]
            results['cross_type_analysis'] = {
                'avg_similarity': float(np.mean(sims)),
                'std_similarity': float(np.std(sims)),
                'min_similarity': float(np.min(sims)),
                'max_similarity': float(np.max(sims)),
                'sample_pairs': cross_type_similarities[:20]  # 保存前20个样本
            }
            
        return results
        
    def _analyze_evidence_similarity_distribution(self, query_cases: List[CaseNode], 
                                                 case_library: List[CaseNode]) -> Dict:
        """分析证据相似度分布"""
        
        # 统计证据类型分布
        evidence_types = {}
        all_cases = query_cases + case_library
        
        for case in all_cases:
            for evidence in case.key_evidence_list:
                if evidence not in evidence_types:
                    evidence_types[evidence] = 0
                evidence_types[evidence] += 1
                
        # 分析高频证据的相似度影响
        high_freq_evidences = {k: v for k, v in evidence_types.items() if v >= 10}
        
        results = {
            'total_evidence_types': len(evidence_types),
            'high_frequency_evidences': high_freq_evidences,
            'evidence_similarity_matrix': {}
        }
        
        # 计算高频证据之间的相似度
        high_freq_list = list(high_freq_evidences.keys())[:10]  # 取前10个高频证据
        
        for i, evidence1 in enumerate(high_freq_list):
            for j, evidence2 in enumerate(high_freq_list[i+1:], i+1):
                vec1 = self.vectorizer.vectorize([evidence1])
                vec2 = self.vectorizer.vectorize([evidence2])
                sim = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
                
                key = f"{evidence1}_{evidence2}"
                results['evidence_similarity_matrix'][key] = float(sim)
                
        return results

    def _convert_case_to_research_input(self, case: CaseNode) -> LegalResearchInput:
        """将CaseNode转换为LegalResearchInput"""
        research_input = LegalResearchInput()
        research_input.case_type = case.case_type
        research_input.core_evidence_list = case.key_evidence_list

        # 从案例事实中提取关键事实
        if case.case_facts_summary:
            sentences = case.case_facts_summary.split('。')
            research_input.key_facts = [s.strip() for s in sentences if s.strip()][:5]
        else:
            research_input.key_facts = ["无关键事实"]

        # 从判决结果中提取诉讼请求
        if case.judgment_result:
            claim = LegalClaimNode()
            claim.claim_id = f"claim_{case.case_id}"
            claim.text = case.judgment_result
            claim.claim_type = "主要请求"
            research_input.legal_claims = [claim]
        else:
            research_input.legal_claims = []

        return research_input

    def _calculate_match_quality(self, query_case: CaseNode, match_case: CaseNode) -> float:
        """计算匹配质量分数"""
        quality_score = 0.0

        # 1. 案由匹配 (权重: 0.3)
        if query_case.case_type == match_case.case_type:
            quality_score += 0.3

        # 2. 法院层级匹配 (权重: 0.2)
        court_hierarchy = {"最高院": 4, "高院": 3, "中院": 2, "基层": 1}
        query_level = court_hierarchy.get(query_case.court_level, 0)
        match_level = court_hierarchy.get(match_case.court_level, 0)

        if query_level == match_level:
            quality_score += 0.2
        elif abs(query_level - match_level) == 1:
            quality_score += 0.1

        # 3. 证据重合度 (权重: 0.5)
        query_evidence = set(query_case.key_evidence_list)
        match_evidence = set(match_case.key_evidence_list)

        if query_evidence and match_evidence:
            intersection = len(query_evidence.intersection(match_evidence))
            union = len(query_evidence.union(match_evidence))
            evidence_overlap = intersection / union if union > 0 else 0
            quality_score += 0.5 * evidence_overlap

        return quality_score

    def save_analysis_results(self, filename: str = None):
        """保存分析结果到JSON文件"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"cosine_similarity_analysis_{timestamp}.json"

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)

        logger.info(f"分析结果已保存到: {filename}")
        return filename


def main():
    """主函数"""
    print("=" * 80)
    print("余弦相似度对案例检索效果影响分析")
    print("=" * 80)

    try:
        # 创建分析器
        analyzer = CosineSimilarityAnalyzer()

        # 运行分析
        print("\n开始余弦相似度影响分析...")
        results = analyzer.analyze_cosine_similarity_impact(
            num_cases=1000,  # 使用1000个案例
            num_queries=100  # 测试100个查询
        )

        if not results:
            print("分析失败，请检查数据路径和配置")
            return False

        # 保存结果
        json_file = analyzer.save_analysis_results()

        # 生成报告
        print("\n生成分析报告...")
        from cosine_similarity_report import CosineSimilarityReporter
        reporter = CosineSimilarityReporter(results)

        # 生成Markdown报告
        report = reporter.generate_report()
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f"cosine_similarity_report_{timestamp}.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)

        # 生成JSON报告（用于集成到现有的聚类分析结果中）
        json_output = reporter.generate_json_output()
        json_report_file = f"cosine_similarity_analysis_report_{timestamp}.json"
        with open(json_report_file, 'w', encoding='utf-8') as f:
            json.dump(json_output, f, ensure_ascii=False, indent=2)

        print(f"\nMarkdown报告已保存到: {report_file}")
        print(f"JSON报告已保存到: {json_report_file}")
        print(f"详细数据已保存到: {json_file}")

        # 显示关键结果
        print("\n" + "=" * 80)
        print("关键分析结果摘要")
        print("=" * 80)

        threshold_analysis = results['threshold_analysis']

        print("\n不同相似度阈值下的检索效果:")
        for threshold in sorted(threshold_analysis.keys()):
            data = threshold_analysis[threshold]
            print(f"  阈值 {threshold}: 成功率 {data['success_rate']*100:.1f}%, "
                  f"案由匹配率 {data['case_type_match_rate']*100:.1f}%")

        # 找出最佳阈值
        best_threshold = None
        best_score = 0
        for threshold, data in threshold_analysis.items():
            score = (data['success_rate'] * 0.4 +
                    data['case_type_match_rate'] * 0.3 +
                    data.get('avg_quality', 0) * 0.3)
            if score > best_score:
                best_score = score
                best_threshold = threshold

        if best_threshold:
            print(f"\n推荐相似度阈值: {best_threshold}")
            best_data = threshold_analysis[best_threshold]
            print(f"  - 成功匹配率: {best_data['success_rate']*100:.1f}%")
            print(f"  - 案由匹配率: {best_data['case_type_match_rate']*100:.1f}%")
            print(f"  - 平均质量分数: {best_data.get('avg_quality', 0):.3f}")

        case_type_analysis = results['case_type_analysis']
        if ('same_type_analysis' in case_type_analysis and
            'cross_type_analysis' in case_type_analysis and
            case_type_analysis['same_type_analysis']):

            same_type_avg = np.mean([case_type_analysis['same_type_analysis'][ct]['avg_similarity']
                                    for ct in case_type_analysis['same_type_analysis']])
            cross_type_avg = case_type_analysis['cross_type_analysis']['avg_similarity']

            print(f"\n案由影响分析:")
            print(f"  - 同案由内平均相似度: {same_type_avg:.3f}")
            print(f"  - 跨案由平均相似度: {cross_type_avg:.3f}")
            print(f"  - 差异: {same_type_avg - cross_type_avg:.3f}")

        print("\n" + "=" * 80)
        print("✓ 余弦相似度分析完成！")
        print("=" * 80)

        return True

    except Exception as e:
        print(f"\n✗ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
