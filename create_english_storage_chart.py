#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Storage Savings Chart Generator with English Case Names
Display storage savings for civil and criminal case types with English translations
"""

import json
import matplotlib.pyplot as plt
import numpy as np

# Set Times New Roman font
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['font.size'] = 10

def load_data(filename):
    """Load JSON data"""
    with open(filename, 'r', encoding='utf-8') as f:
        return json.load(f)

def translate_case_names():
    """Create mapping of Chinese case names to English"""
    translations = {
        # Civil cases
        '民间借贷纠纷': 'Private Lending',
        '金融借款合同纠纷': 'Bank Loan Contract',
        '机动车交通事故责任纠纷': 'Traffic Accident',
        '买卖合同纠纷': 'Sales Contract',
        '信用卡纠纷': 'Credit Card',
        '物业服务合同纠纷': 'Property Service',
        '劳务合同纠纷': 'Labor Contract',
        '商品房预售合同纠纷': 'Pre-sale Housing',
        '房屋买卖合同纠纷': 'House Sale',
        '劳动争议': 'Labor Dispute',
        '房屋租赁合同纠纷': 'House Rental',
        '商品房销售合同纠纷': 'Housing Sales',
        '提供劳务者受害责任纠纷': 'Service Provider Liability',
        '合同纠纷': 'Contract Dispute',
        '追偿权纠纷': 'Recourse Rights',
        
        # Criminal cases
        '盗窃': 'Theft',
        '危险驾驶': 'Dangerous Driving',
        '故意伤害': 'Intentional Injury',
        '交通肇事': 'Traffic Accident Crime',
        '走私、贩卖、运输、制造毒品': 'Drug Trafficking',
        '诈骗': 'Fraud',
        '容留他人吸毒': 'Drug Harboring',
        '寻衅滋事': 'Picking Quarrels',
        '滥伐林木': 'Illegal Logging',
        '信用卡诈骗': 'Credit Card Fraud',
        '抢劫': 'Robbery',
        '开设赌场': 'Gambling House',
        '妨害公务': 'Obstruct Official',
        '故意毁坏财物': 'Property Damage'
    }
    return translations

def extract_case_data(data):
    """Extract case data with English translations"""
    civil_cases = []
    criminal_cases = []
    translations = translate_case_names()

    # Extract selected high frequency civil cases
    for case in data['民事案由聚类效果']['高频案由'][:6]:
        english_name = translations.get(case['案由'], case['案由'])
        civil_cases.append({
            'name': case['案由'],
            'name_en': english_name,
            'reduction_rate': float(case['减少率'].rstrip('%')),
            'original_count': case['原始数量'],
            'saved_cases': case['节省案例']
        })

    # Extract selected medium frequency civil cases
    for case in data['民事案由聚类效果']['中频案由'][:3]:
        english_name = translations.get(case['案由'], case['案由'])
        civil_cases.append({
            'name': case['案由'],
            'name_en': english_name,
            'reduction_rate': float(case['减少率'].rstrip('%')),
            'original_count': case['原始数量'],
            'saved_cases': case['节省案例']
        })

    # Extract selected high frequency criminal cases
    for case in data['刑事案由聚类效果']['高频案由'][:6]:
        english_name = translations.get(case['案由'], case['案由'])
        criminal_cases.append({
            'name': case['案由'],
            'name_en': english_name,
            'reduction_rate': float(case['减少率'].rstrip('%')),
            'original_count': case['原始数量'],
            'saved_cases': case['节省案例']
        })

    # Extract selected medium frequency criminal cases
    for case in data['刑事案由聚类效果']['中频案由'][:3]:
        english_name = translations.get(case['案由'], case['案由'])
        criminal_cases.append({
            'name': case['案由'],
            'name_en': english_name,
            'reduction_rate': float(case['减少率'].rstrip('%')),
            'original_count': case['原始数量'],
            'saved_cases': case['节省案例']
        })

    return civil_cases, criminal_cases

def create_chart(civil_cases, criminal_cases):
    """Create improved storage savings comparison chart with English names"""
    # Combine all cases for single chart
    all_cases = civil_cases + criminal_cases
    
    # Create figure
    fig, ax = plt.subplots(figsize=(18, 8))
    
    # Extract data
    case_names = [case['name_en'] for case in all_cases]
    reduction_rates = [case['reduction_rate'] for case in all_cases]
    original_counts = [case['original_count'] for case in all_cases]
    
    # Define Morandi colors (muted blue and red)
    base_grey = '#7B9BB0'  # Morandi blue for civil cases
    base_red = '#B07B7B'   # Morandi red for criminal cases
    
    # Create color list
    colors = [base_grey] * len(civil_cases) + [base_red] * len(criminal_cases)
    
    # Create bars with softer appearance
    bars = ax.bar(range(len(case_names)), reduction_rates, color=colors, alpha=0.85,
                  edgecolor='#F5F5F5', linewidth=1.2)
    
    # Customize chart with softer styling
    ax.set_title('Legal Case Storage Savings Comparison',
                fontsize=16, fontweight='bold', pad=20, color='#555555')
    ax.set_ylabel('Storage Savings Rate (%)', fontsize=12, fontweight='bold', color='#666666')
    ax.set_xlabel('Case Types', fontsize=12, fontweight='bold', color='#666666')
    ax.set_ylim(0, 100)
    
    # Set x-axis labels with muted color
    ax.set_xticks(range(len(case_names)))
    ax.set_xticklabels(case_names, rotation=45, ha='right', fontsize=10, fontweight='bold', color='#666666')
    
    # Add value labels on bars (percentage only)
    for i, (bar, rate) in enumerate(zip(bars, reduction_rates)):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{rate:.1f}%',
                ha='center', va='bottom', fontsize=9, fontweight='bold',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.9, edgecolor='#E8E8E8'))
    
    # Add subtle grid
    ax.grid(axis='y', alpha=0.25, linestyle='--', linewidth=0.6, color='#D3D3D3')
    ax.set_axisbelow(True)

    # Customize spines with muted colors
    for spine in ax.spines.values():
        spine.set_linewidth(1.0)
        spine.set_color('#CCCCCC')
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    
    # Add average lines
    civil_avg = np.mean([case['reduction_rate'] for case in civil_cases])
    criminal_avg = np.mean([case['reduction_rate'] for case in criminal_cases])
    
    # Add average line for civil cases
    civil_end = len(civil_cases) - 0.5
    ax.axhline(y=civil_avg, xmin=0, xmax=civil_end/len(all_cases), 
               color=base_grey, linestyle='--', linewidth=2, alpha=0.7)
    
    # Add average line for criminal cases  
    criminal_start = len(civil_cases) - 0.5
    ax.axhline(y=criminal_avg, xmin=criminal_start/len(all_cases), xmax=1,
               color=base_red, linestyle='--', linewidth=2, alpha=0.7)

    # Add legend without frame
    from matplotlib.patches import Patch
    legend_elements = [
        Patch(facecolor=base_grey, label=f'Civil Cases (Avg: {civil_avg:.1f}%)'),
        Patch(facecolor=base_red, label=f'Criminal Cases (Avg: {criminal_avg:.1f}%)')
    ]
    ax.legend(handles=legend_elements, loc='upper right', fontsize=11, frameon=False)
    
    # Adjust layout
    plt.tight_layout()

    # Save chart
    plt.savefig('storage_savings_comparison_improved.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.show()
    
    # Print statistics
    print("\n=== Storage Savings Statistics ===")
    print(f"Civil cases average savings: {civil_avg:.1f}%")
    print(f"Criminal cases average savings: {criminal_avg:.1f}%")
    civil_counts = [case['original_count'] for case in civil_cases]
    criminal_counts = [case['original_count'] for case in criminal_cases]
    print(f"Civil cases total original count: {sum(civil_counts)}")
    print(f"Criminal cases total original count: {sum(criminal_counts)}")
    print(f"Civil cases total saved: {sum([case['saved_cases'] for case in civil_cases])}")
    print(f"Criminal cases total saved: {sum([case['saved_cases'] for case in criminal_cases])}")

def main():
    """Main function"""
    try:
        # Load data
        data = load_data('cleg_clustering_analysis.json')
        
        # Extract case data
        civil_cases, criminal_cases = extract_case_data(data)
        
        # Create chart
        create_chart(civil_cases, criminal_cases)
        
        print("Chart generated and saved as 'storage_savings_comparison_improved.png'")
        
    except FileNotFoundError:
        print("Error: File 'cleg_clustering_analysis.json' not found")
    except Exception as e:
        print(f"Error generating chart: {e}")

if __name__ == "__main__":
    main()
