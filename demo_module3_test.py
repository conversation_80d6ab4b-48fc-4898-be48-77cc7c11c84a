#!/usr/bin/env python3
"""
模块三性能对比测试演示
展示优化方案与传统方案的性能差异
"""

import sys
import os
import time
import logging
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

from src.performance_benchmark import DataGenerator
from tests.test_module3_comparison import Module3ComparisonTest, DirectCaseRetriever
from src.legal_research_module import LegalResearchModule

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def demo_basic_comparison():
    """演示基本性能对比"""
    print("🚀 模块三性能对比演示")
    print("="*60)
    
    # 生成测试数据
    print("\n📊 生成测试数据...")
    provisions = DataGenerator.generate_legal_provisions(100)
    case_data = DataGenerator.generate_case_nodes(500)
    queries = DataGenerator.generate_test_queries(10)
    
    print(f"✅ 生成了 {len(provisions)} 个法条")
    print(f"✅ 生成了 {len(case_data)} 个案例")
    print(f"✅ 生成了 {len(queries)} 个查询")
    
    # 转换案例数据
    test = Module3ComparisonTest()
    cases = test._convert_case_data(case_data)
    
    # 初始化优化方案
    print("\n⚡ 初始化优化方案...")
    start_time = time.time()
    optimized_module = LegalResearchModule()
    optimized_module.initialize(provisions, case_data)
    opt_init_time = time.time() - start_time
    print(f"✅ 优化方案初始化完成，耗时: {opt_init_time:.2f}秒")
    
    # 初始化传统方案
    print("\n🔄 初始化传统方案...")
    start_time = time.time()
    direct_retriever = DirectCaseRetriever()
    direct_retriever.initialize(cases)
    direct_init_time = time.time() - start_time
    print(f"✅ 传统方案初始化完成，耗时: {direct_init_time:.2f}秒")
    
    # 性能对比测试
    print("\n🏃‍♂️ 开始查询性能测试...")
    
    opt_times = []
    direct_times = []
    
    for i, query in enumerate(queries[:5]):  # 测试前5个查询
        print(f"\n查询 {i+1}: {query.case_type}")
        print(f"证据: {', '.join(query.core_evidence_list[:3])}...")
        
        # 优化方案查询
        start = time.time()
        opt_results = optimized_module.case_recommender.recommend_similar_cases(query, top_k=5)
        opt_time = time.time() - start
        opt_times.append(opt_time)
        
        # 传统方案查询
        start = time.time()
        direct_results = direct_retriever.retrieve_cases(
            query.core_evidence_list, query.case_type, top_k=5
        )
        direct_time = time.time() - start
        direct_times.append(direct_time)
        
        print(f"  优化方案: {opt_time*1000:.2f}ms, 返回 {len(opt_results)} 个结果")
        print(f"  传统方案: {direct_time*1000:.2f}ms, 返回 {len(direct_results)} 个结果")
        
        # 显示速度提升
        if direct_time > 0:
            improvement = (direct_time - opt_time) / direct_time * 100
            print(f"  🚀 速度提升: {improvement:.1f}%")
    
    # 总结
    print("\n📈 性能测试总结")
    print("="*40)
    
    avg_opt_time = sum(opt_times) / len(opt_times) * 1000
    avg_direct_time = sum(direct_times) / len(direct_times) * 1000
    avg_improvement = (avg_direct_time - avg_opt_time) / avg_direct_time * 100
    
    print(f"平均查询时间:")
    print(f"  优化方案: {avg_opt_time:.2f}ms")
    print(f"  传统方案: {avg_direct_time:.2f}ms")
    print(f"  平均提升: {avg_improvement:.1f}%")
    
    print(f"\n初始化时间:")
    print(f"  优化方案: {opt_init_time:.2f}s")
    print(f"  传统方案: {direct_init_time:.2f}s")
    
    return {
        'avg_opt_time': avg_opt_time,
        'avg_direct_time': avg_direct_time,
        'improvement': avg_improvement,
        'opt_init_time': opt_init_time,
        'direct_init_time': direct_init_time
    }


def demo_accuracy_comparison():
    """演示准确率对比"""
    print("\n🎯 准确率对比演示")
    print("="*40)
    
    # 生成测试数据
    provisions = DataGenerator.generate_legal_provisions(50)
    case_data = DataGenerator.generate_case_nodes(200)
    queries = DataGenerator.generate_test_queries(5)
    
    test = Module3ComparisonTest()
    cases = test._convert_case_data(case_data)
    
    # 初始化系统
    optimized_module = LegalResearchModule()
    optimized_module.initialize(provisions, case_data)
    
    direct_retriever = DirectCaseRetriever()
    direct_retriever.initialize(cases)
    
    print(f"测试 {len(queries)} 个查询的准确率...")
    
    total_opt_precision = 0
    total_direct_precision = 0
    valid_queries = 0
    
    for i, query in enumerate(queries):
        print(f"\n查询 {i+1}: {query.case_type}")
        
        # 生成标准答案
        ground_truth = test._generate_ground_truth(query, cases)
        if not ground_truth:
            print("  跳过（无相关案例）")
            continue
            
        relevant_ids = set(ground_truth)
        print(f"  标准答案: {len(relevant_ids)} 个相关案例")
        
        # 优化方案结果
        opt_results = optimized_module.case_recommender.recommend_similar_cases(query, top_k=10)
        opt_retrieved = set([case.case_id for case, score in opt_results])
        
        # 传统方案结果
        direct_results = direct_retriever.retrieve_cases(
            query.core_evidence_list, query.case_type, top_k=10
        )
        direct_retrieved = set([case.case_id for case, score in direct_results])
        
        # 计算精确率
        opt_precision = len(opt_retrieved.intersection(relevant_ids)) / len(opt_retrieved) if opt_retrieved else 0
        direct_precision = len(direct_retrieved.intersection(relevant_ids)) / len(direct_retrieved) if direct_retrieved else 0
        
        print(f"  优化方案精确率: {opt_precision:.3f}")
        print(f"  传统方案精确率: {direct_precision:.3f}")
        
        total_opt_precision += opt_precision
        total_direct_precision += direct_precision
        valid_queries += 1
    
    if valid_queries > 0:
        avg_opt_precision = total_opt_precision / valid_queries
        avg_direct_precision = total_direct_precision / valid_queries
        precision_improvement = (avg_opt_precision - avg_direct_precision) / avg_direct_precision * 100 if avg_direct_precision > 0 else 0
        
        print(f"\n📊 准确率总结:")
        print(f"  优化方案平均精确率: {avg_opt_precision:.3f}")
        print(f"  传统方案平均精确率: {avg_direct_precision:.3f}")
        print(f"  精确率提升: {precision_improvement:.1f}%")
        
        return {
            'opt_precision': avg_opt_precision,
            'direct_precision': avg_direct_precision,
            'precision_improvement': precision_improvement
        }
    
    return None


def demo_storage_comparison():
    """演示存储占用对比"""
    print("\n💾 存储占用对比演示")
    print("="*40)
    
    provisions = DataGenerator.generate_legal_provisions(1000)
    case_data = DataGenerator.generate_case_nodes(2000)
    
    test = Module3ComparisonTest()
    cases = test._convert_case_data(case_data)
    
    # 计算存储占用
    opt_storage = test._calculate_optimized_storage(provisions, cases)
    direct_storage = test._calculate_direct_storage(provisions, cases)
    
    print(f"优化方案存储占用:")
    print(f"  总存储: {opt_storage['total_mb']:.1f} MB")
    print(f"  向量存储: {opt_storage['vectors_mb']:.1f} MB")
    print(f"  文本存储: {opt_storage['text_mb']:.1f} MB")
    print(f"  索引存储: {opt_storage['index_mb']:.1f} MB")
    
    print(f"\n传统方案存储占用:")
    print(f"  总存储: {direct_storage['total_mb']:.1f} MB")
    print(f"  向量存储: {direct_storage['vectors_mb']:.1f} MB")
    print(f"  文本存储: {direct_storage['text_mb']:.1f} MB")
    print(f"  索引存储: {direct_storage['index_mb']:.1f} MB")
    
    storage_overhead = (opt_storage['total_mb'] - direct_storage['total_mb']) / direct_storage['total_mb'] * 100
    print(f"\n存储开销: {storage_overhead:.1f}%")
    
    return {
        'opt_storage': opt_storage['total_mb'],
        'direct_storage': direct_storage['total_mb'],
        'overhead': storage_overhead
    }


def main():
    """主演示函数"""
    print("🎭 模块三性能对比完整演示")
    print("="*80)
    
    # 1. 基本性能对比
    speed_results = demo_basic_comparison()
    
    # 2. 准确率对比
    accuracy_results = demo_accuracy_comparison()
    
    # 3. 存储对比
    storage_results = demo_storage_comparison()
    
    # 最终总结
    print("\n🏆 演示总结")
    print("="*50)
    
    if speed_results:
        print(f"⚡ 查询速度提升: {speed_results['improvement']:.1f}%")
    
    if accuracy_results:
        print(f"🎯 准确率提升: {accuracy_results['precision_improvement']:.1f}%")
    
    if storage_results:
        print(f"💾 存储开销: {storage_results['overhead']:.1f}%")
    
    print("\n✨ 结论:")
    print("优化方案通过增加适量的存储开销，显著提升了查询速度和准确率，")
    print("特别适合大规模法律案例库的实时检索应用。")
    
    print(f"\n📝 演示完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    main()
