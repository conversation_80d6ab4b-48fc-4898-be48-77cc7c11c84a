# 模块一：案件解构模块 (Case Deconstruction Module) 实现文档 (LLM-Centric)

## 1. 模块概述

### 1.1 功能定位
案件解构模块是"法弈"系统的核心信息处理器，其功能定位是利用大语言模型（LLM）的强大自然语言理解能力，将用户输入的非结构化案情描述文本，一步式地转化为机器可读、逻辑严谨的异构法律知识图谱（Heterogeneous Legal Knowledge Graph, HLKG）。

### 1.2 核心职责
- **统一知识抽取**: 通过调用大模型，同步完成实体、事件、关系等所有核心法律要素的识别与抽取。
- **结构化转换**: 将大模型返回的信息，精准映射到预定义的知识图谱数据结构中。
- **逻辑一致性校验**: 对生成的图谱进行内部逻辑和时序关系的初步验证。
- **知识图谱构建**: 组装节点与关系，生成最终的HLKG实例。

## 2. 数据结构设计

(注：数据结构作为系统的目标模型，保持不变，因为它们定义了我们希望从大模型中获得的结构化输出。)

### 2.1 输入数据结构

```python
from typing import List, Dict
from datetime import datetime

class CaseInput:
    """案件输入数据结构"""
    def __init__(self):
        self.case_id: str = ""           # 案件唯一标识
        self.user_id: str = ""           # 用户标识
        self.fact_description: str = ""   # 事实陈述文本
        self.legal_claims: List[str] = [] # 核心诉求列表
        self.timestamp: datetime = None   # 输入时间戳
        self.metadata: Dict = {}         # 元数据信息
```

### 2.2 知识图谱节点结构

```python
class GraphNode:
    """图谱节点基类"""
    def __init__(self):
        self.node_id: str = ""           # 节点唯一标识
        self.node_type: str = ""         # 节点类型
        self.properties: Dict = {}       # 节点属性
        self.confidence: float = 0.0     # 置信度分数

class PersonNode(GraphNode):
    """人物节点"""
    # ... (内容同前)

class OrganizationNode(GraphNode):
    """组织节点"""
    # ... (内容同前)

class EventNode(GraphNode):
    """事件节点"""
    # ... (内容同前)

class EvidenceNode(GraphNode):
    """证据节点"""
    # ... (内容同前)

class LegalClaimNode(GraphNode):
    """诉求节点"""
    # ... (内容同前)

class LegalElementNode(GraphNode):
    """法律要件节点"""
    # ... (内容同前)
```

### 2.3 关系结构

```python
class GraphRelation:
    """图谱关系结构"""
    def __init__(self):
        self.relation_id: str = ""       # 关系唯一标识
        self.source_node_id: str = ""    # 源节点ID
        self.target_node_id: str = ""    # 目标节点ID
        self.relation_type: str = ""     # 关系类型
        self.properties: Dict = {}       # 关系属性
        self.confidence: float = 0.0     # 置信度
        self.weight: float = 1.0         # 权重
```

### 2.4 输出数据结构

```python
class HLKG:
    """异构法律知识图谱"""
    def __init__(self):
        self.graph_id: str = ""          # 图谱标识
        self.case_id: str = ""           # 关联案件ID
        self.nodes: Dict[str, GraphNode] = {} # 节点字典
        self.relations: List[GraphRelation] = [] # 关系列表
        self.metadata: Dict = {}         # 元数据
        self.creation_time: datetime = None
        self.version: str = "1.0"
```

## 3. 核心算法设计：基于大模型的统一知识抽取

核心算法围绕一个统一的知识抽取器展开，该抽取器通过向大模型发送包含任务描述、Schema定义和输出格式要求的复杂指令，来引导其完成从文本到结构化知识的转换。

```python
class LLMKnowledgeExtractor:
    """基于大模型的统一知识抽取器"""

    def __init__(self, llm_client):
        self.llm_client = llm_client  # 大模型API客户端

    def extract_knowledge_graph(self, text: str) -> Dict:
        """
        基于大模型进行图谱元素抽取的主算法。

        算法流程:
        1. **动态指令生成**: 基于待分析文本，动态构建一个结构化的分析任务指令。该指令清晰地定义了需要抽取的节点和关系类型、所需的输出JSON结构，并可能包含少量示例以引导模型更好地理解任务。
        2. **调用大模型**: 将生成的指令和案情文本发送给大模型，并显式要求其以JSON格式返回分析结果。调用时配置较低的temperature参数以保证输出的确定性和稳定性。
        3. **解析与验证**: 对大模型返回的JSON字符串进行解析。验证其顶级结构是否包含"nodes"和"relations"键，以及这些列表的内容是否基本符合预期的数据类型。
        4. **后处理与标准化**: 对解析后的数据进行规范化处理，例如，为所有节点和关系生成唯一的UUID作为标识符，并将文本中的日期字符串统一转换为ISO 8601标准格式。
        """
        # 步骤1: 构建分析任务指令
        prompt = self._build_knowledge_extraction_prompt(text)

        # 步骤2: 调用大模型并获取结构化输出
        llm_response = self.llm_client.generate(
            prompt=prompt,
            temperature=0.1,
            max_tokens=8000,
            response_format={"type": "json_object"}
        )

        # 步骤3 & 4: 解析、验证和后处理
        try:
            graph_data = self._parse_and_postprocess(llm_response)
            return graph_data
        except Exception as e:
            # 记录错误并返回空结构，或触发重试逻辑
            print(f"解析大模型输出时出错: {e}")
            return {"nodes": [], "relations": []}

    def _build_knowledge_extraction_prompt(self, text: str) -> str:
        """
        私有方法，负责根据文本和预设的图谱Schema，动态生成一个复杂的、
        结构化的任务指令，用于引导大语言模型。
        """
        # 此处为指令工程的具体实现，不对外暴露
        pass

    def _parse_and_postprocess(self, llm_response: str) -> Dict:
        """
        私有方法，负责解析、验证和标准化大模型的输出。
        """
        # ... 实现JSON解析、Schema验证、ID生成、数据标准化等逻辑
        pass
```

## 4. 质量控制与验证

质量控制的重心在于对大模型输出结果进行自动化、多维度的验证和修正。

### 4.1 逻辑一致性检查
- **引用完整性**: 自动化脚本检查所有`GraphRelation`对象中的`source_node_id`和`target_node_id`是否存在于`nodes`字典中。任何悬挂的边都将被标记为错误。
- **时序合理性**: 提取所有`EventNode`的时间戳，构建案件时间轴。系统自动检查是否存在逻辑矛盾，例如，刑事案件中“伤害行为”的时间点必须早于“死亡结果”的时间点；民事案件中“立案”事件必须在“开庭”事件之前。
- **角色冲突检测**: 检查同一`PersonNode`是否被赋予了互斥的角色。例如，在同一个案件中，一个自然人不能同时被识别为“原告”和“被告”。

### 4.2 置信度评估与自洽修正
- **LLM自评估置信度**: 在任务指令中要求大模型为每一个抽取的节点和关系提供一个`confidence`浮点数分数。系统可以设定一个阈值（如0.75），低于该阈值的所有抽取结果将被标记，以待进一步审核。
- **二次校验与修正**: 对于低置信度或存在逻辑冲突的抽取结果，系统可以自动触发“自我修正”流程。例如，如果模型抽取了一个置信度为0.6的“盗窃”事件，系统将生成一个新的指令：“你先前从文本中识别出一个‘盗窃’事件，但置信度较低。请重新审阅原文中关于‘被告人如何获取财物’的描述，并确认是否存在‘秘密窃取’的关键要素。如果存在，请确认并提高置信度；如果不存在，请修正事件类型或删除该事件。”

### 4.3 错误处理
- **JSON解析失败**: 若LLM返回的不是合法的JSON，系统将自动重试最多3次。若依然失败，则将该案件标记为“抽取失败”，并记录原始输出以供分析。
- **Schema结构不匹配**: 如果返回的JSON结构有效，但字段或类型与预定义Schema不符（例如，`EventNode`缺少`timestamp`），系统将尝试进行类型转换或填充默认值，同时记录一个“结构警告”。
- **空洞抽取**: 如果模型返回的`nodes`和`relations`列表均为空，系统将判定为“无效抽取”，并将其移交人工处理队列。

## 5. 性能优化

### 5.1 算法与调用优化
- **批量处理**: 如果API支持，系统可以将多个案件（例如，同一批次上传的10个案件）的抽取请求打包，在一次API调用中处理，从而减少网络开销，提高整体吞吐量。
- **结果缓存**: 系统采用基于输入文本内容的哈希值作为缓存键。在处理新案件时，首先计算其`fact_description`的哈希值，如果命中缓存，则直接从缓存（如Redis）中读取已存的知识图谱结果，避免重复的LLM API调用。
- **增量式图谱更新**: 当用户为一个已处理的案件补充新材料时，系统不会重新处理全文，而是将“已有图谱的摘要”、“原始文本”和“新补充的文本”一同发送给LLM，并要求其在现有图谱基础上进行增量更新，大幅提升效率。

### 5.2 存储优化
- **图数据库存储与索引**: 将生成的HLKG存储在专业的图数据库（如Neo4j, NebulaGraph）中。同时，为高频查询的节点属性创建索引，例如，为`PersonNode`的`name`和`identity_info`字段、`EventNode`的`event_type`字段建立索引，这将使“查询所有涉及张三的案件”或“检索所有类型为‘合同诈骗’的事件”这类查询的速度提升数个数量级。
- **图谱分区存储**:
    - **按案件类型分区**: 在数据库层面，为不同法域的案件创建独立的逻辑图空间或数据库实例。例如，设立“民事案件库”、“刑事案件库”和“行政案件库”。查询一个民间借贷纠纷时，只需在“民事案件库”中进行，极大缩小了搜索范围。
    - **按时间范围分区**: 对于拥有海量历史数据的系统，可按案件的立案年份进行物理分区或表分区（如`cases_2023`, `cases_2024`）。近期活跃的案件数据存放在高性能存储介质（热数据），而超过5年的历史案件则归档到成本较低的存储（冷数据）。
- **高效内存数据结构**: 在数据加载和服务层，对图谱的关键部分采用优化的内存结构。例如，使用Roaring Bitmaps来高效存储和计算节点之间的大规模邻接关系，或使用压缩字典树（Trie）来存储实体名称，从而在保证查询性能的同时，显著降低内存占用。

## 6. 接口设计

```python
class CaseDeconstructionModule:
    """案件解构模块主接口"""

    def __init__(self, llm_client):
        self.extractor = LLMKnowledgeExtractor(llm_client)

    def process_case(self, case_input: CaseInput) -> HLKG:
        """
        处理单个案件输入，调用大模型生成并验证知识图谱。
        """
        # ... (实现同前)

    def validate_graph(self, graph: HLKG) -> bool:
        """
        对生成的图谱进行自动化质量验证，执行第4节中描述的各类检查。
        """
        # ... (实现具体的验证逻辑)
        return True

    def update_graph(self, graph: HLKG, new_info: str) -> HLKG:
        """
        基于新信息，调用大模型对一个已有的知识图谱进行增量式更新。
        """
        # ... (实现增量更新的复杂指令构建和调用逻辑)
        pass
```