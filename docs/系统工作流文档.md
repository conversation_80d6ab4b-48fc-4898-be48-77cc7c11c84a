# "法弈"智能法律辅助系统 - 系统工作流文档

## 1. 文档概述

本文档详细描述了"法弈"智能法律辅助系统四个核心模块之间的数据流动、输入输出关系以及完整的工作流程。系统采用流水线式处理架构，各模块按顺序协同工作，最终生成优化的诉讼策略和法律文书。

## 2. 系统整体工作流程

### 2.1 数据流向图

```
用户输入(案情描述)
    ↓
┌─────────────────────────────────────────────────────────────┐
│ 模块一：案件解构模块 (Case Deconstruction Module)            │
│ 输入：CaseInput                                             │
│ 输出：HLKG (异构法律知识图谱)                                │
└─────────────────────────────────────────────────────────────┘
    ↓ (HLKG + 法律要件)
    ├─────────────────────────────────────────────────────────┐
    ↓                                                         ↓
┌─────────────────────────────────────────┐  ┌─────────────────────────────────────────┐
│ 模块二：证据策略模块                     │  │ 模块三：法律研究模块                     │
│ (Evidence Strategy Module)              │  │ (Legal Research Module)                │
│ 输入：EvidenceStrategyInput             │  │ 输入：LegalResearchInput                │
│ 输出：EvidenceStrategyOutput            │  │ 输出：LegalResearchOutput               │
└─────────────────────────────────────────┘  └─────────────────────────────────────────┘
    ↓ (证据策略)                                ↓ (法律研究结果)
    └─────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────┐
│ 模块四：多角色模拟法庭推演模块                               │
│ (Multi-Role Court Simulation Module)                       │
│ 输入：CourtSimulationInput                                  │
│ 输出：CourtSimulationOutput                                 │
└─────────────────────────────────────────────────────────────┘
    ↓
最终输出(优化策略 + 法律文书)
```

### 2.2 处理阶段划分

1. **信息解构阶段**：模块一处理非结构化文本
2. **策略准备阶段**：模块二、三并行处理，生成证据策略和法律研究结果
3. **对抗验证阶段**：模块四进行多智能体模拟推演
4. **成果输出阶段**：生成最终的优化策略和法律文书

## 3. 各模块详细工作流程

### 3.1 模块一：案件解构模块

#### 3.1.1 输入数据结构
```python
class CaseInput:
    case_id: str                    # 案件唯一标识
    user_id: str                    # 用户标识
    fact_description: str           # 事实陈述文本 (主要输入)
    legal_claims: List[str]         # 核心诉求列表
    timestamp: datetime             # 输入时间戳
    metadata: Dict                  # 元数据信息
```

#### 3.1.2 核心处理流程
1. **统一知识抽取**
   - 调用大语言模型进行实体、事件、关系抽取
   - 动态指令生成与结构化输出解析
   - 生成包含人物、组织、事件、证据、诉求、法律要件等节点

2. **逻辑一致性校验**
   - 引用完整性检查
   - 时序合理性验证
   - 角色冲突检测

3. **知识图谱构建**
   - 组装节点与关系
   - 生成异构法律知识图谱(HLKG)

#### 3.1.3 输出数据结构
```python
class HLKG:
    graph_id: str                   # 图谱标识
    case_id: str                    # 关联案件ID
    nodes: Dict[str, GraphNode]     # 节点字典 (人物、组织、事件、证据、诉求、法律要件)
    relations: List[GraphRelation]  # 关系列表 (参与、导致、证明、支撑等)
    metadata: Dict                  # 元数据
    creation_time: datetime         # 创建时间
    version: str                    # 版本信息
```

#### 3.1.4 向后续模块的数据传递
- **传递给模块二**：完整的HLKG + 提取的法律要件节点
- **传递给模块三**：HLKG中的关键事实和诉讼请求
- **传递给模块四**：完整的HLKG作为推演基础

### 3.2 模块二：证据策略模块

#### 3.2.1 输入数据结构
```python
class EvidenceStrategyInput:
    hlkg: HLKG                              # 来自模块一的知识图谱
    legal_elements: List[LegalElementNode]  # 来自模块一的法律要件
    case_type: str                          # 案件类型
    legal_claims: List[LegalClaimNode]      # 诉讼请求
    case_description: str                   # 案件基本事实描述
```

#### 3.2.2 核心处理流程
1. **基于微调LLM的证据生成**
   - 使用在刑事、民事案件数据上微调的专用大语言模型
   - 根据案情描述直接生成核心证据列表
   - 每个证据包含名称和证明目的

2. **基于知识图谱的证据链构建**
   - 证据节点实例化与映射
   - 证据链路径搜索与构建
   - 证据链强度与风险评估

3. **证据缺口诊断**
   - 覆盖度检查：识别未被证明的法律要件
   - 强度检查：评估证据链是否达到阈值
   - 生成补强建议：推荐需要补充的证据类型

#### 3.2.3 输出数据结构
```python
class EvidenceStrategyOutput:
    core_evidence_list: List[EvidenceAssessment]  # 核心证据清单及评估
    evidence_chains: List[EvidenceChain]          # 构建的证据链
    gap_report: EvidenceGapReport                 # 证据缺口分析报告
    strategy_recommendations: List[str]           # 综合策略建议
    evidence_matrix: Dict                         # 证据-要件二维关联矩阵
    risk_assessment: Dict                         # 详细风险评估
```

#### 3.2.4 关键输出组件说明
- **EvidenceChain**: 包含目标要件、主要证据、辅助证据、强度评分、薄弱环节
- **EvidenceGap**: 包含缺口类型、严重程度、建议补充证据、替代策略
- **EvidenceAssessment**: 包含相关性、真实性、可采性、证明力评分

### 3.3 模块三：法律研究模块

#### 3.3.1 输入数据结构
```python
class LegalResearchInput:
    hlkg: HLKG                              # 来自模块一的知识图谱
    core_evidence_list: List[str]           # 来自模块二的核心证据列表
    case_type: str                          # 案件类型
    legal_claims: List[LegalClaimNode]      # 诉讼请求节点
    key_facts: List[str]                    # 关键事实
    jurisdiction: str                       # 管辖区域
```

#### 3.3.2 核心处理流程
1. **法律条款混合检索**
   - 语义检索：使用Legal-BERT等模型进行向量检索
   - 关键词检索：使用BM25算法进行精确匹配
   - 结果融合：通过RRF算法合并两种检索结果

2. **相似案例推荐(Mem-Graph机制)**
   - 证据列表向量化与聚类
   - 分层预筛选：高频案由使用聚类中心策略，低频案由使用向量索引
   - 深度相似度计算：综合证据、事实、法律争议相似度
   - 覆盖关系处理：过滤被覆盖的陈旧案例

3. **动态案例库管理**
   - ADD操作：添加新案例并建立关系
   - 覆盖关系检查：层级覆盖和证据覆盖
   - 案例效力状态更新

#### 3.3.3 输出数据结构
```python
class LegalResearchOutput:
    relevant_provisions: List[dict]     # 相关法条列表
    similar_cases: List[dict]           # 相似案例推荐
    precedent_trends: Dict              # 判例趋势分析
    risk_assessment: Dict               # 风险评估报告
```

#### 3.3.4 关键输出组件说明
- **relevant_provisions**: 包含法条ID、名称、条款号、内容、相关性评分
- **similar_cases**: 包含案例基本信息、事实摘要、法院观点、判决结果、核心证据
- **precedent_trends**: 基于历史案例的判决趋势分析
- **risk_assessment**: 基于相似案例的风险预警

### 3.4 模块四：多角色模拟法庭推演模块

#### 3.4.1 输入数据结构
```python
class CourtSimulationInput:
    hlkg: HLKG                                  # 来自模块一的知识图谱
    evidence_strategy: EvidenceStrategyOutput   # 来自模块二的证据策略
    legal_research: LegalResearchOutput         # 来自模块三的法律研究结果
    case_context: Dict                          # 案件上下文
    simulation_config: Dict                     # 模拟配置参数
```

#### 3.4.2 核心处理流程
1. **智能体初始化**
   - 原告智能体：基于证据策略和法律依据
   - 被告智能体：基于证据缺口制定攻击策略
   - 法官智能体：基于法律研究结果建立判决标准

2. **对抗式推演**
   - 按争议焦点逐一进行论证交锋
   - 原告举证：选择最强证据链构建论证
   - 被告反驳：针对薄弱环节发起攻击
   - 法官评估：综合评估论证强度

3. **策略优化**
   - 识别核心弱点：分析失败的交锋
   - 生成改进建议：针对弱点提出优化方案
   - 制定应对策略：预判对方攻击并制定预案

#### 3.4.3 输出数据结构
```python
class CourtSimulationOutput:
    optimized_arguments: List[Dict]         # 优化后的论证方案
    risk_assessment: Dict[str, str]         # 核心风险点识别
    weakness_analysis: Dict                 # 弱点分析报告
    counter_strategies: List[Dict]          # 应对策略
    evidence_optimization: Dict             # 证据优化建议
    final_recommendations: List[str]        # 最终策略建议
    simulation_report: SimulationSession    # 完整推演过程报告
```

## 4. 模块间数据依赖关系

### 4.1 数据流依赖图
```
CaseInput → 模块一 → HLKG
                    ↓
                    ├→ 模块二 → EvidenceStrategyOutput
                    │           ↓
                    └→ 模块三 → LegalResearchOutput
                                ↓
                    模块四 ← (HLKG + EvidenceStrategyOutput + LegalResearchOutput)
                        ↓
                CourtSimulationOutput
```

### 4.2 关键数据传递点

1. **模块一 → 模块二**
   - 传递：完整HLKG + 法律要件节点
   - 用途：证据链构建的基础图结构

2. **模块一 → 模块三**
   - 传递：关键事实 + 诉讼请求
   - 用途：法条检索和案例推荐的查询条件

3. **模块二 → 模块三**
   - 传递：核心证据列表
   - 用途：相似案例推荐的核心特征

4. **模块二 → 模块四**
   - 传递：证据策略输出(含证据缺口)
   - 用途：被告攻击策略制定

5. **模块三 → 模块四**
   - 传递：法律研究结果
   - 用途：法官判决标准建立

### 4.3 反馈循环机制

1. **模块四 → 模块二反馈**
   - 证据优化建议反馈给证据策略模块
   - 形成持续优化的闭环

2. **模块三 → 模块三自更新**
   - 新案例自动加入Mem-Graph
   - 覆盖关系动态更新

## 5. 数据质量保证机制

### 5.1 数据一致性检查
- 模块间数据格式标准化
- 必要字段完整性验证
- 数据类型匹配检查

### 5.2 错误处理机制
- 上游模块输出异常时的降级处理
- 数据缺失时的默认值填充
- 处理失败时的重试机制

### 5.3 质量评估指标
- 知识图谱构建准确率
- 证据链覆盖完整性
- 法条检索相关性
- 推演结果可信度

## 6. 性能优化策略

### 6.1 并行处理优化
- 模块二、三可并行执行
- 批量处理多个案件
- 异步数据传递

### 6.2 缓存机制
- 知识图谱结果缓存
- 法条检索结果缓存
- 相似案例推荐缓存

### 6.3 增量更新
- 知识图谱增量更新
- 案例库动态扩展
- 模型参数在线调优

## 7. 系统集成接口

### 7.1 模块间标准接口
```python
# 主工作流控制器
class LegalAssistantWorkflow:
    def process_case(self, case_input: CaseInput) -> CourtSimulationOutput:
        # 1. 案件解构
        hlkg = self.module1.process_case(case_input)
        
        # 2. 并行处理证据策略和法律研究
        evidence_input = EvidenceStrategyInput(hlkg=hlkg, ...)
        research_input = LegalResearchInput(hlkg=hlkg, ...)
        
        evidence_output = self.module2.analyze_evidence_strategy(evidence_input)
        research_output = self.module3.conduct_legal_research(research_input)
        
        # 3. 模拟法庭推演
        simulation_input = CourtSimulationInput(
            hlkg=hlkg,
            evidence_strategy=evidence_output,
            legal_research=research_output
        )
        
        final_output = self.module4.run_simulation(simulation_input)
        
        return final_output
```

### 7.2 外部系统接口
- 用户输入接口：接收案情描述
- 结果输出接口：返回优化策略
- 数据库接口：存储处理结果
- 监控接口：系统状态监控

## 8. 总结

"法弈"系统通过四个模块的有机协作，实现了从非结构化案情描述到优化诉讼策略的全流程智能化处理。各模块间通过标准化的数据接口进行信息传递，形成了完整的数据处理流水线。系统设计充分考虑了数据质量保证、性能优化和错误处理，确保了整体系统的稳定性和可靠性。
