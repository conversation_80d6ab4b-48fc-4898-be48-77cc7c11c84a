# **模块二：证据策略模块 (Evidence Strategy Module) 实现文档 (V2.1 - 完整详细版)**

## 1. 模块概述

### 1.1 功能定位
证据策略模块是系统的核心推理引擎，负责根据案件的具体事实和诉讼请求，**智能生成核心证据列表**，并**结合异构法律知识图谱构建严谨、完整的证据链**。同时，本模块还能精准诊断证据缺口、评估证据风险，为整体诉讼策略提供强有力的自动化决策支持。

### 1.2 核心职责
- **基于微调大模型的证据列表生成**：利用在海量刑事、民事案件数据上微调的专用大语言模型，根据案情直接生成高度相关的核心证据项。
- **基于知识图谱的证据链构建**：将生成的证据与案件的法律要件在知识图谱中进行关联，搜索并构建逻辑严谨、覆盖全面的证据链。
- **证据缺口诊断与补强建议**：通过对比证据链与法律要件的覆盖情况，精准识别证据缺口，并提出智能化的补强建议。
- **证据链风险评估与预警**：分析证据链的薄弱环节（如孤证、有瑕疵证据），预警证据被质证或不被采纳的风险。

## 2. 数据结构设计

### 2.1 输入数据结构

```python
class EvidenceStrategyInput:
    """证据策略模块输入"""
    def __init__(self):
        self.hlkg: HLKG = None                    # (来自模块一) 异构法律知识图谱
        self.legal_elements: List[LegalElementNode] = [] # (来自模块一) 待证明的法律要件
        self.case_type: str = ""                  # 案件类型 (如: 民事, 刑事)
        self.legal_claims: List[LegalClaimNode] = [] # 诉讼请求
        self.case_description: str = ""           # 案件基本事实描述
```

### 2.2 证据评估结构

```python
class EvidenceAssessment:
    """证据评估结果"""
    def __init__(self):
        self.evidence_id: str = ""               # 证据ID
        self.evidence_name: str = ""             # 证据名称 (例如: "银行转账记录")
        self.relevance_score: float = 0.0        # 相关性评分(0-1)
        self.authenticity_score: float = 0.0     # 真实性评分(0-1)
        self.admissibility_score: float = 0.0    # 可采性评分(0-1)
        self.probative_value: float = 0.0        # 证明力评分(0-1)
        self.risk_factors: List[str] = []        # 风险因素 (例如: "孤证", "传来证据")
        self.supporting_elements: List[str] = [] # 支撑的法律要件名称```

### 2.3 证据链与缺口分析结构

```python
class EvidenceChain:
    """证据链结构"""
    def __init__(self):
        self.chain_id: str = ""                  # 证据链ID
        self.target_element: str = ""            # 目标证明的法律要件
        self.primary_evidence: List[str] = []    # 主要证据ID列表
        self.supporting_evidence: List[str] = [] # 辅助证据ID列表
        self.chain_strength: float = 0.0         # 证据链强度评分
        self.weak_points: List[Dict] = []        # 薄弱环节描述
        self.logical_flow: List[Dict] = []       # 逻辑流程 (描述证据如何一步步证明要件)

class EvidenceGap:
    """证据缺口"""
    def __init__(self):
        self.gap_id: str = ""                    # 缺口ID
        self.target_element: str = ""            # 未被充分证明的目标要件
        self.gap_type: str = ""                  # 缺口类型 (例如: "完全缺失", "证明力不足")
        self.severity: str = ""                  # 严重程度(高/中/低)
        self.description: str = ""               # 缺口描述
        self.suggested_evidence: List[str] = []  # 建议补充的证据类型
        self.alternative_strategies: List[str] = [] # 替代策略
        self.impact_assessment: str = ""         # 对诉讼请求的影响评估

class EvidenceGapReport:
    """证据缺口报告"""
    def __init__(self):
        self.case_id: str = ""                   # 案件ID
        self.gaps: List[EvidenceGap] = []        # 所有证据缺口列表
        self.overall_strength: float = 0.0       # 整体证据强度
        self.critical_gaps: List[str] = []       # 关键缺口ID列表
        self.recommendations: List[str] = []     # 总体策略建议
        self.risk_level: str = ""                # 整体诉讼风险等级 (高/中/低)
```

### 2.4 输出数据结构

```python
class EvidenceStrategyOutput:
    """证据策略输出"""
    def __init__(self):
        self.core_evidence_list: List[EvidenceAssessment] = [] # 核心证据清单及评估
        self.evidence_chains: List[EvidenceChain] = []         # 构建的证据链
        self.gap_report: EvidenceGapReport = None              # 证据缺口分析报告
        self.strategy_recommendations: List[str] = []          # 综合策略建议
        self.evidence_matrix: Dict = {}                        # 证据-要件二维关联矩阵
        self.risk_assessment: Dict = {}                        # 详细风险评估
```

## 3. 核心算法设计

### 3.1 核心证据生成算法 (基于微调LLM)

**算法概述：**
本算法的核心是利用一个在特定法律领域数据集上经过监督式微调（Supervised Fine-Tuning, SFT）的大语言模型，直接从案情描述生成初步的证据列表。该方法借鉴了 `CLEG` 论文中的研究成果，即任务专属的微调能显著提升模型在证据生成任务上的表现。我们构建的**刑事与民事证据列表数据集**，确保了模型训练数据的专业性和逻辑严谨性，使其能够精准地理解案件事实与所需证据之间的复杂映射关系。

```python
class FineTunedEvidenceGenerator:
    """基于微调LLM的证据生成器"""

    def __init__(self, model_path: str):
        # 加载在刑事/民事证据列表数据集上微调后的模型
        self.model = self._load_finetuned_model(model_path)

    def generate_evidence_list(self, case_description: str, legal_claims: List[str]) -> List[Dict]:
        """
        根据案情和诉求，生成初步的核心证据列表

        算法流程:
        1.  **输入构建**：将案件描述（case_description）和核心诉讼请求（legal_claims）整合成结构化的输入文本。该文本遵循模型在微调阶段所适应的格式，确保模型能够准确理解任务。
        2.  **模型推理**：调用微调后的大语言模型进行推理。模型基于其在海量案例中学到的“范式”，直接生成一段包含多个证据项的文本。每个证据项都应包含“证据名称”和其旨在证明的“证明目的”。例如，输出可能为："1. 银行转账记录，用于证明原告已将借款支付给被告。 2. 借款合同，用于证明双方存在借贷合意。"
        3.  **解析与结构化**：设计一个强大的解析器（例如，基于正则表达式或进一步的LLM调用），将模型输出的自然语言文本，精确地转换为标准化的数据结构列表，如 `List[{'evidence_name': str, 'purpose': str}]`，为后续送入知识图谱处理做好准备。
        """
        # 具体的实现逻辑在此处展开
        pass
```

### 3.2 证据链构建与缺口诊断算法 (基于知识图谱)

**算法概述：**
此算法是模块的推理核心，负责将上一步生成的“点状”证据，在异构法律知识图谱（HLKG）中进行串联，形成具有逻辑关系的“链条”，并与案件必须证明的法律要件进行对齐，从而评估证据的完整性并识别缺口。

```python
class KG_EvidenceChainBuilder:
    """基于知识图谱的证据链构建与分析器"""

    def __init__(self, hlkg: HLKG):
        self.kg = hlkg  # 模块一的知识图谱实例

    def build_and_analyze(self, generated_evidence: List[Dict],
                          legal_elements: List[LegalElementNode]) -> (List[EvidenceChain], EvidenceGapReport):
        """
        构建证据链、评估完整性并识别缺口

        算法流程:
        1.  **证据节点实例化与映射**：
            -   **实体链接**：遍历 `generated_evidence` 列表。对每一个证据（如“银行转账记录”），在知识图谱中查找对应的标准证据类型节点（`EvidenceTypeNode`）。
            -   **创建实例**：为本次案件的每个具体证据，在图的会话层创建一个临时的“证据实例节点”（`EvidenceInstanceNode`）。这个实例节点继承了其类型节点的属性，并可附加案件特有的信息（如金额、日期）。
            -   **目的映射**：根据证据的“证明目的”文本，通过语义相似度计算和图谱中的`proves`关系，将该证据实例节点链接到它试图证明的一个或多个法律要件节点（`LegalElementNode`）上。

        2.  **证据链路径搜索与构建**：
            -   **目标驱动搜索**：遍历所有待证明的 `legal_elements`。对每一个要件（如“完成款项交付”），将其设为目标节点。
            -   **图遍历**：从该目标节点开始，在知识图谱中进行反向图遍历（如使用BFS或DFS算法），搜索所有能够直接或间接指向该节点的 `EvidenceInstanceNode`。
            -   **构建链条**：一条或多条从证据实例节点到目标要件节点的路径，即构成一条证据链。例如，`[银行转账记录实例] -> (proves) -> [完成款项交付]` 是一条简单的直接证据链。而 `[聊天记录实例] -> (corroborates) -> [银行转账记录实例] -> (proves) -> [完成款项交付]` 则是一条包含辅助证据的更强证据链。

        3.  **证据链强度与风险评估**：
            -   **强度量化**：为每条构建的证据链计算“强度分”。评分模型可设计为：`Strength = Σ(w_i * P_i) + C`。其中 `P_i` 是链上第i个证据的证明力（probative_value），`w_i` 是其权重（主要证据权重高），`C` 是证据间的协同加分（例如，多种不同类型证据相互印证则`C > 0`）。
            -   **风险识别**：
                -   **孤证风险**：如果一条证据链只由一个证据实例构成，则标记为“孤证风险”。
                -   **类型风险**：如果证据链依赖于知识图谱中预先标记为“弱证明力”的证据类型（如无其他证据佐证的单一证人证言），则标记为“类型风险”。
                -   **逻辑断裂**：如果一个复杂要件的多个子要件只有部分被覆盖，则标记为“逻辑不完整风险”。

        4.  **证据缺口诊断与建议生成**：
            -   **覆盖度检查**：创建一个所有 `legal_elements` 的集合，并用所有证据链覆盖的 `target_element` 集合与之求差集。差集中的元素即为存在“完全缺失”缺口的法律要件。
            -   **强度检查**：对于已被覆盖的要件，检查其对应证据链的强度分是否达到预设的阈值。未达标的，则判定为“证明力不足”的缺口。
            -   **生成补强建议**：对于每一个缺口要件，正向查询知识图谱：查询所有与该 `LegalElementNode` 有 `is_proven_by` 关系的 `EvidenceTypeNode`。将这些证据类型的名称作为具体的“建议补充证据”返回。
        """
        # 具体的实现逻辑在此处展开
        pass
```

## 4. 证据策略与风险评估

### 4.1 证据组合与举证顺序优化
- **证据组合优化**: 基于`KG_EvidenceChainBuilder`构建的证据链及其强度评分，系统可以进行智能组合。对于能证明同一要件的多条证据链，优先选择强度最高、风险最低的链条作为主要证明路径，其他链条的证据可作为补充或备用，从而实现证据的精简与高效。
- **举证顺序优化**: 知识图谱中的法律要件之间可能存在逻辑上的依赖关系（例如，先证明“合同成立”再证明“合同违约”）。系统可以根据这种依赖关系拓扑排序，生成一个逻辑清晰、说服力强的推荐举证顺序。

### 4.2 风险评估与应对策略
- **证据链断裂风险**: 风险评估结果将明确指出哪些法律要件的证明链条是薄弱的，并说明原因（如上文提到的孤证风险、类型风险等）。这为律师提前准备应对策略提供了明确的指引。
- **应对策略生成**:
    - **补强方案**: 由缺口诊断算法直接生成，明确告知需要补充何种类型的证据来弥补缺口。
    - **替代证据方案**: 当某个关键证据（如原件丢失）存在瑕疵时，系统可以在知识图谱中搜索具有相似证明功能的其他证据类型节点，提供替代方案建议。例如，对于证明借贷关系，若无借条原件，可推荐“银行转账记录+聊天记录”的组合证据。
    - **风险缓解措施**: 针对特定风险，提供策略建议。例如，对于“孤证风险”，建议“寻找其他证人或物证进行补强”；对于“传来证据”，建议“尽力寻找原始证据或核实渠道”。

## 5. 接口设计

```python
class EvidenceStrategyModule:
    """证据策略模块主接口"""

    def analyze_evidence_strategy(self, input_data: EvidenceStrategyInput) -> EvidenceStrategyOutput:
        """
        分析证据策略的统一入口。该方法将协同调用内部的各个算法组件，
        完成从证据生成到证据链构建、缺口分析和风险评估的全流程。
        """
        # 步骤 1: 初始化核心算法组件
        generator = FineTunedEvidenceGenerator(model_path="path/to/your/finetuned_model")
        chain_builder = KG_EvidenceChainBuilder(hlkg=input_data.hlkg)

        # 步骤 2: 调用微调模型，根据案情生成初步的核心证据列表
        preliminary_evidence = generator.generate_evidence_list(
            input_data.case_description, input_data.legal_claims
        )

        # 步骤 3: 调用基于知识图谱的构建器，完成证据链构建和缺口诊断
        evidence_chains, gap_report = chain_builder.build_and_analyze(
            preliminary_evidence, input_data.legal_elements
        )

        # 步骤 4: 对生成的证据和链条进行最终的评估和整合
        final_evidence_list = self._assess_final_evidence(evidence_chains) # 综合评估每项证据
        final_recommendations = self._generate_overall_recommendations(gap_report) # 生成总体建议

        # 步骤 5: 组装并返回标准化的输出对象
        output = EvidenceStrategyOutput()
        output.core_evidence_list = final_evidence_list
        output.evidence_chains = evidence_chains
        output.gap_report = gap_report
        output.strategy_recommendations = final_recommendations
        # ... (根据需要填充 evidence_matrix 和 risk_assessment)

        return output
```