#!/usr/bin/env python3
"""
基础功能测试脚本
验证法律研究模块的基本功能是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.data_structures import LegalResearchInput, LegalClaimNode
from src.legal_research_module import LegalResearchModule
from src.performance_benchmark import DataGenerator
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_data_generation():
    """测试数据生成"""
    print("测试数据生成...")
    
    # 生成法条
    provisions = DataGenerator.generate_legal_provisions(10)
    assert len(provisions) == 10
    assert provisions[0].provision_id == "provision_000000"
    print(f"✓ 成功生成 {len(provisions)} 个法条")
    
    # 生成案例
    cases = DataGenerator.generate_case_nodes(5)
    assert len(cases) == 5
    assert cases[0]['case_id'] == "case_000000"
    print(f"✓ 成功生成 {len(cases)} 个案例")
    
    # 生成查询
    queries = DataGenerator.generate_test_queries(3)
    assert len(queries) == 3
    assert isinstance(queries[0], LegalResearchInput)
    print(f"✓ 成功生成 {len(queries)} 个查询")


def test_module_initialization():
    """测试模块初始化"""
    print("\n测试模块初始化...")
    
    # 生成测试数据
    provisions = DataGenerator.generate_legal_provisions(50)
    cases = DataGenerator.generate_case_nodes(20)
    
    # 初始化模块
    module = LegalResearchModule()
    module.initialize(provisions, cases)
    
    assert module.is_initialized
    print("✓ 模块初始化成功")
    
    # 检查统计信息
    stats = module.get_module_statistics()
    assert stats['provision_count'] == 50
    assert stats['mem_graph_stats']['total_cases'] == 20
    print(f"✓ 统计信息正确: {stats['provision_count']} 法条, {stats['mem_graph_stats']['total_cases']} 案例")
    
    return module


def test_legal_research():
    """测试法律研究功能"""
    print("\n测试法律研究功能...")
    
    # 初始化模块
    module = test_module_initialization()
    
    # 创建测试查询
    query = LegalResearchInput()
    query.case_type = "民事-合同纠纷"
    query.core_evidence_list = ["合同文本", "转账记录"]
    query.key_facts = ["合同纠纷", "违约责任"]
    
    claim = LegalClaimNode()
    claim.claim_id = "test_claim"
    claim.text = "请求赔偿损失"
    query.legal_claims = [claim]
    
    # 执行研究
    result = module.conduct_legal_research(query)
    
    # 验证结果
    assert isinstance(result.relevant_provisions, list)
    assert isinstance(result.similar_cases, list)
    assert isinstance(result.risk_assessment, dict)
    
    print(f"✓ 法律研究完成")
    print(f"  - 相关法条: {len(result.relevant_provisions)} 个")
    print(f"  - 相似案例: {len(result.similar_cases)} 个")
    print(f"  - 风险评估: {result.risk_assessment}")
    
    return result


def test_dynamic_updates():
    """测试动态更新功能"""
    print("\n测试动态更新功能...")
    
    # 初始化模块
    provisions = DataGenerator.generate_legal_provisions(10)
    initial_cases = DataGenerator.generate_case_nodes(5)
    
    module = LegalResearchModule()
    module.initialize(provisions, initial_cases)
    
    initial_count = module.get_module_statistics()['mem_graph_stats']['total_cases']
    
    # 添加新案例
    new_case_data = {
        'case_id': 'test_new_case',
        'case_name': '测试新案例',
        'court_name': '测试法院',
        'court_level': '基层',
        'case_type': '民事-合同纠纷',
        'case_facts_summary': '测试案例事实',
        'court_opinion': '测试法院观点',
        'judgment_result': '测试判决结果',
        'legal_basis': ['provision_000001'],
        'key_evidence_list': ['合同文本', '证人证言']
    }
    
    success = module.update_mem_graph_with_new_case(new_case_data)
    assert success
    
    updated_count = module.get_module_statistics()['mem_graph_stats']['total_cases']
    assert updated_count == initial_count + 1
    
    print(f"✓ 动态更新成功: {initial_count} -> {updated_count} 案例")


def test_performance_benchmark():
    """测试性能基准"""
    print("\n测试性能基准...")
    
    from src.performance_benchmark import PerformanceBenchmark
    
    benchmark = PerformanceBenchmark()
    
    # 运行小规模测试
    results = benchmark.run_comprehensive_benchmark(
        provision_counts=[10, 20],
        case_counts=[5, 10]
    )
    
    assert 'provision_retrieval' in results
    assert 'case_recommendation' in results
    assert 'memory_usage' in results
    
    print("✓ 性能基准测试完成")
    
    # 生成报告
    report = benchmark.generate_performance_report()
    assert len(report) > 0
    print("✓ 性能报告生成成功")


def main():
    """主测试函数"""
    print("=" * 50)
    print("法律研究模块基础功能测试")
    print("=" * 50)
    
    try:
        # 运行各项测试
        test_data_generation()
        test_module_initialization()
        test_legal_research()
        test_dynamic_updates()
        test_performance_benchmark()
        
        print("\n" + "=" * 50)
        print("✓ 所有测试通过！")
        print("=" * 50)
        print("\n模块功能验证完成，可以运行完整演示:")
        print("python -c 'from src.demo import main; main()'")
        
    except Exception as e:
        print(f"\n✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
