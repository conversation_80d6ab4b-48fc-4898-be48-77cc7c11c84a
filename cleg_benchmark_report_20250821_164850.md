# CLEG数据基准测试报告
生成时间: 2025-08-21 16:48:50
测试配置: 100 案例, 200 法条, 50 查询

## 检索速度对比

| 检索方法 | 初始化时间(s) | 平均查询时间(s) | QPS | 内存使用(MB) |
|----------|---------------|-----------------|-----|-------------|
| 混合检索 | 1.315 | 0.016 | 63.4 | 63.3 |
| 传统检索 | 0.000 | 0.050 | 20.0 | 0.0 |
| Elasticsearch | 19.667 | 0.024 | 41.0 | 0.0 |

## 检索准确率对比

| 检索方法 | 精确率 | 召回率 | F1分数 |
|----------|--------|--------|--------|
| 混合检索 | 0.000 | 0.000 | 0.000 |
| 传统检索 | 0.000 | 0.000 | 0.000 |
| Elasticsearch | 0.000 | 0.000 | 0.000 |

## 性能提升分析

### 检索速度提升
- 相比传统检索: 68.5%
- 相比Elasticsearch: 35.3%

### 内存使用优化
- 相比传统检索: 0.0%
- 相比Elasticsearch: 0.0%

### 检索准确率提升
- F1分数相比传统检索: 0.0%
- F1分数相比Elasticsearch: 0.0%

