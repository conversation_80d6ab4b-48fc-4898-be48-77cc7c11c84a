#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Storage Savings Chart Generator
Display storage savings for civil and criminal case types
"""

import json
import matplotlib.pyplot as plt
import numpy as np

# Set Times New Roman font
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['font.size'] = 10

def load_data(filename):
    """Load JSON data"""
    with open(filename, 'r', encoding='utf-8') as f:
        return json.load(f)

def extract_case_data(data):
    """Extract case data"""
    civil_cases = []
    criminal_cases = []

    # Extract selected high frequency civil cases
    for case in data['民事案由聚类效果']['高频案由'][:4]:
        civil_cases.append({
            'name': case['案由'],
            'reduction_rate': float(case['减少率'].rstrip('%')),
            'original_count': case['原始数量'],
            'saved_cases': case['节省案例']
        })

    # Extract selected medium frequency civil cases
    for case in data['民事案由聚类效果']['中频案由'][:2]:
        civil_cases.append({
            'name': case['案由'],
            'reduction_rate': float(case['减少率'].rstrip('%')),
            'original_count': case['原始数量'],
            'saved_cases': case['节省案例']
        })

    # Extract selected high frequency criminal cases
    for case in data['刑事案由聚类效果']['高频案由'][:4]:
        criminal_cases.append({
            'name': case['案由'],
            'reduction_rate': float(case['减少率'].rstrip('%')),
            'original_count': case['原始数量'],
            'saved_cases': case['节省案例']
        })

    # Extract selected medium frequency criminal cases
    for case in data['刑事案由聚类效果']['中频案由'][:2]:
        criminal_cases.append({
            'name': case['案由'],
            'reduction_rate': float(case['减少率'].rstrip('%')),
            'original_count': case['原始数量'],
            'saved_cases': case['节省案例']
        })

    return civil_cases, criminal_cases

def create_chart(civil_cases, criminal_cases):
    """Create bar chart"""
    # Set figure size for single chart with better proportions
    fig, ax = plt.subplots(1, 1, figsize=(14, 6))
    
    # Prepare data for combined chart
    civil_names = [f"Civil {i+1}" for i in range(len(civil_cases))]
    criminal_names = [f"Criminal {i+1}" for i in range(len(criminal_cases))]
    all_names = civil_names + criminal_names

    civil_rates = [case['reduction_rate'] for case in civil_cases]
    criminal_rates = [case['reduction_rate'] for case in criminal_cases]
    all_rates = civil_rates + criminal_rates

    # Define specified colors
    base_grey = '#3E6281'  # Specified grey
    base_red = '#C54C61'   # Specified red

    # Create gradient colors
    from matplotlib.colors import to_rgba
    import matplotlib.colors as mcolors

    # Create gradient for civil cases (grey)
    grey_colors = []
    for i in range(len(civil_cases)):
        # Create gradient from lighter to darker
        alpha = 0.6 + (i / (len(civil_cases) - 1)) * 0.4  # From 0.6 to 1.0
        grey_colors.append(mcolors.to_rgba(base_grey, alpha))

    # Create gradient for criminal cases (red)
    red_colors = []
    for i in range(len(criminal_cases)):
        # Create gradient from lighter to darker
        alpha = 0.6 + (i / (len(criminal_cases) - 1)) * 0.4  # From 0.6 to 1.0
        red_colors.append(mcolors.to_rgba(base_red, alpha))

    # Combine colors
    colors = grey_colors + red_colors

    # Create combined bar chart
    bars = ax.bar(range(len(all_names)), all_rates, color=colors, alpha=0.8,
                  edgecolor='white', linewidth=1)

    # Set chart properties
    ax.set_ylabel('Storage Savings (%)', fontsize=12)
    ax.set_ylim(0, 100)
    ax.grid(axis='y', alpha=0.3, linestyle='--')

    # Set x-axis labels
    ax.set_xticks(range(len(all_names)))
    ax.set_xticklabels(all_names, rotation=45, ha='right', fontsize=10)

    # Add value labels on bars
    for i, (bar, rate) in enumerate(zip(bars, all_rates)):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{rate:.1f}%',
                ha='center', va='bottom', fontsize=9)

    # Add legend
    from matplotlib.patches import Patch
    legend_elements = [Patch(facecolor=base_grey, label='Civil Cases'),
                      Patch(facecolor=base_red, label='Criminal Cases')]
    ax.legend(handles=legend_elements, loc='upper right', fontsize=11)
    
    # Adjust layout
    plt.tight_layout()

    # Save chart
    plt.savefig('storage_savings_comparison_improved.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.show()
    
    # Print statistics
    print("\n=== Storage Savings Statistics ===")
    print(f"Civil cases average savings: {np.mean(civil_rates):.1f}%")
    print(f"Criminal cases average savings: {np.mean(criminal_rates):.1f}%")
    civil_counts = [case['original_count'] for case in civil_cases]
    criminal_counts = [case['original_count'] for case in criminal_cases]
    print(f"Civil cases total original count: {sum(civil_counts)}")
    print(f"Criminal cases total original count: {sum(criminal_counts)}")
    print(f"Civil cases total saved: {sum([case['saved_cases'] for case in civil_cases])}")
    print(f"Criminal cases total saved: {sum([case['saved_cases'] for case in criminal_cases])}")
    
    # Print case type mapping
    print("\n=== Case Type Mapping ===")
    print("Civil Cases:")
    for i, case in enumerate(civil_cases):
        print(f"  Case {i+1}: {case['name']} ({case['reduction_rate']:.1f}% savings)")
    
    print("\nCriminal Cases:")
    for i, case in enumerate(criminal_cases):
        print(f"  Case {i+1}: {case['name']} ({case['reduction_rate']:.1f}% savings)")

def main():
    """Main function"""
    try:
        # Load data
        data = load_data('cleg_clustering_analysis.json')
        
        # Extract case data
        civil_cases, criminal_cases = extract_case_data(data)
        
        # Create chart
        create_chart(civil_cases, criminal_cases)
        
        print("Chart generated and saved as 'storage_savings_comparison_improved.png'")
        
    except FileNotFoundError:
        print("Error: File 'cleg_clustering_analysis.json' not found")
    except Exception as e:
        print(f"Error generating chart: {e}")

if __name__ == "__main__":
    main()
