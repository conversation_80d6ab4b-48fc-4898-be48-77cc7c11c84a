#!/usr/bin/env python3
"""
聚类前后相似度对比分析
比较聚类前同案由内所有案例的平均相似度 vs 聚类后各聚类中心的平均相似度
"""

import sys
import os
import json
import numpy as np
from typing import List, Dict, Tuple, Any
import logging
from datetime import datetime
from collections import defaultdict

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.evidence_vectorizer import EvidenceVectorizer
from test_cleg_benchmark import CLEGDataLoader, CLEGBenchmark

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ClusteringSimilarityComparator:
    """聚类前后相似度对比分析器"""
    
    def __init__(self):
        self.data_loader = CLEGDataLoader()
        self.benchmark = CLEGBenchmark()
        self.vectorizer = EvidenceVectorizer()
        self.results = {}
        
    def analyze_clustering_similarity_impact(self, num_cases: int = 2000) -> Dict:
        """分析聚类前后相似度变化"""
        logger.info(f"开始聚类前后相似度对比分析 - 案例数: {num_cases}")
        
        # 加载数据
        civil_cases = self.data_loader.load_civil_cases(limit=num_cases//2)
        criminal_cases = self.data_loader.load_criminal_cases(limit=num_cases//2)
        all_cases = civil_cases + criminal_cases
        
        if len(all_cases) == 0:
            logger.error("无法加载CLEG数据，请检查数据路径")
            return {}
            
        logger.info(f"成功加载 {len(all_cases)} 个案例")
        
        # 转换为CaseNode格式
        case_nodes = []
        for i, case_data in enumerate(all_cases):
            case_node = self.benchmark._convert_cleg_to_case_node(case_data, i)
            case_nodes.append(case_node)
            
        # 按案由分组
        case_type_groups = defaultdict(list)
        for case in case_nodes:
            case_type_groups[case.case_type].append(case)
            
        logger.info(f"共有 {len(case_type_groups)} 个不同案由")
        
        # 分析每个案由的聚类前后相似度
        case_type_results = {}
        
        for case_type, cases in case_type_groups.items():
            if len(cases) < 5:  # 跳过案例数太少的案由
                continue
                
            logger.info(f"分析案由: {case_type} ({len(cases)} 个案例)")
            
            # 聚类前：计算同案由内所有案例的平均相似度
            pre_clustering_similarity = self._calculate_pre_clustering_similarity(cases)
            
            # 执行聚类
            clusters = self._perform_clustering(cases)
            
            # 聚类后：计算各聚类中心的平均相似度
            post_clustering_similarity = self._calculate_post_clustering_similarity(clusters)
            
            # 计算聚类内部相似度
            intra_cluster_similarity = self._calculate_intra_cluster_similarity(clusters)
            
            # 计算聚类间相似度
            inter_cluster_similarity = self._calculate_inter_cluster_similarity(clusters)
            
            case_type_results[case_type] = {
                '案例数量': len(cases),
                '聚类前平均相似度': pre_clustering_similarity,
                '聚类后中心相似度': post_clustering_similarity,
                '聚类内部平均相似度': intra_cluster_similarity,
                '聚类间平均相似度': inter_cluster_similarity,
                '聚类数量': len(clusters),
                '相似度提升': post_clustering_similarity - pre_clustering_similarity,
                '聚类效果评分': self._calculate_clustering_score(
                    pre_clustering_similarity, post_clustering_similarity, 
                    intra_cluster_similarity, inter_cluster_similarity
                )
            }
            
        # 计算总体统计
        overall_stats = self._calculate_overall_statistics(case_type_results)
        
        # 整合结果
        results = {
            'test_config': {
                'total_cases': len(case_nodes),
                'case_types_count': len(case_type_results),
                'timestamp': datetime.now().isoformat()
            },
            'case_type_analysis': case_type_results,
            'overall_statistics': overall_stats
        }
        
        self.results = results
        return results
        
    def _calculate_pre_clustering_similarity(self, cases: List) -> float:
        """计算聚类前同案由内所有案例的平均相似度"""
        if len(cases) < 2:
            return 0.0
            
        similarities = []
        
        # 计算所有案例两两之间的相似度
        for i in range(len(cases)):
            for j in range(i + 1, len(cases)):
                vec1 = self.vectorizer.vectorize(cases[i].key_evidence_list)
                vec2 = self.vectorizer.vectorize(cases[j].key_evidence_list)
                
                # 计算余弦相似度
                similarity = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
                similarities.append(similarity)
                
        return float(np.mean(similarities)) if similarities else 0.0
        
    def _perform_clustering(self, cases: List) -> List[List]:
        """执行聚类，基于证据相似度"""
        if len(cases) <= 1:
            return [cases]
            
        # 计算所有案例的向量
        vectors = []
        for case in cases:
            vec = self.vectorizer.vectorize(case.key_evidence_list)
            vectors.append(vec)
            
        # 简单的基于相似度阈值的聚类
        clusters = []
        used = set()
        similarity_threshold = 0.7  # 相似度阈值
        
        for i, case in enumerate(cases):
            if i in used:
                continue
                
            cluster = [case]
            used.add(i)
            
            # 找到与当前案例相似的其他案例
            for j, other_case in enumerate(cases):
                if j in used or i == j:
                    continue
                    
                similarity = np.dot(vectors[i], vectors[j]) / (
                    np.linalg.norm(vectors[i]) * np.linalg.norm(vectors[j])
                )
                
                if similarity >= similarity_threshold:
                    cluster.append(other_case)
                    used.add(j)
                    
            clusters.append(cluster)
            
        return clusters
        
    def _calculate_post_clustering_similarity(self, clusters: List[List]) -> float:
        """计算聚类后各聚类中心的平均相似度"""
        if len(clusters) < 2:
            return 0.0
            
        # 计算每个聚类的中心向量
        cluster_centers = []
        for cluster in clusters:
            if not cluster:
                continue
                
            # 计算聚类中心（平均向量）
            vectors = []
            for case in cluster:
                vec = self.vectorizer.vectorize(case.key_evidence_list)
                vectors.append(vec)
                
            center = np.mean(vectors, axis=0)
            cluster_centers.append(center)
            
        # 计算聚类中心之间的平均相似度
        similarities = []
        for i in range(len(cluster_centers)):
            for j in range(i + 1, len(cluster_centers)):
                similarity = np.dot(cluster_centers[i], cluster_centers[j]) / (
                    np.linalg.norm(cluster_centers[i]) * np.linalg.norm(cluster_centers[j])
                )
                similarities.append(similarity)
                
        return float(np.mean(similarities)) if similarities else 0.0
        
    def _calculate_intra_cluster_similarity(self, clusters: List[List]) -> float:
        """计算聚类内部平均相似度"""
        all_intra_similarities = []
        
        for cluster in clusters:
            if len(cluster) < 2:
                continue
                
            cluster_similarities = []
            for i in range(len(cluster)):
                for j in range(i + 1, len(cluster)):
                    vec1 = self.vectorizer.vectorize(cluster[i].key_evidence_list)
                    vec2 = self.vectorizer.vectorize(cluster[j].key_evidence_list)
                    
                    similarity = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
                    cluster_similarities.append(similarity)
                    
            if cluster_similarities:
                all_intra_similarities.extend(cluster_similarities)
                
        return float(np.mean(all_intra_similarities)) if all_intra_similarities else 0.0
        
    def _calculate_inter_cluster_similarity(self, clusters: List[List]) -> float:
        """计算聚类间平均相似度"""
        if len(clusters) < 2:
            return 0.0
            
        inter_similarities = []
        
        for i in range(len(clusters)):
            for j in range(i + 1, len(clusters)):
                cluster1 = clusters[i]
                cluster2 = clusters[j]
                
                # 计算两个聚类之间所有案例对的相似度
                for case1 in cluster1:
                    for case2 in cluster2:
                        vec1 = self.vectorizer.vectorize(case1.key_evidence_list)
                        vec2 = self.vectorizer.vectorize(case2.key_evidence_list)
                        
                        similarity = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
                        inter_similarities.append(similarity)
                        
        return float(np.mean(inter_similarities)) if inter_similarities else 0.0
        
    def _calculate_clustering_score(self, pre_sim: float, post_sim: float, 
                                   intra_sim: float, inter_sim: float) -> float:
        """计算聚类效果评分"""
        # 好的聚类应该有：
        # 1. 高的聚类内部相似度
        # 2. 低的聚类间相似度
        # 3. 聚类中心相似度适中（不能太高也不能太低）
        
        if intra_sim == 0 or inter_sim == 0:
            return 0.0
            
        # 聚类内外相似度比值（越大越好）
        intra_inter_ratio = intra_sim / max(inter_sim, 0.001)
        
        # 综合评分
        score = intra_inter_ratio * 0.6 + intra_sim * 0.4
        
        return float(score)
        
    def _calculate_overall_statistics(self, case_type_results: Dict) -> Dict:
        """计算总体统计"""
        if not case_type_results:
            return {}
            
        pre_similarities = [result['聚类前平均相似度'] for result in case_type_results.values()]
        post_similarities = [result['聚类后中心相似度'] for result in case_type_results.values()]
        intra_similarities = [result['聚类内部平均相似度'] for result in case_type_results.values()]
        inter_similarities = [result['聚类间平均相似度'] for result in case_type_results.values()]
        improvements = [result['相似度提升'] for result in case_type_results.values()]
        scores = [result['聚类效果评分'] for result in case_type_results.values()]
        
        return {
            '平均聚类前相似度': float(np.mean(pre_similarities)),
            '平均聚类后中心相似度': float(np.mean(post_similarities)),
            '平均聚类内部相似度': float(np.mean(intra_similarities)),
            '平均聚类间相似度': float(np.mean(inter_similarities)),
            '平均相似度提升': float(np.mean(improvements)),
            '平均聚类效果评分': float(np.mean(scores)),
            '聚类前相似度标准差': float(np.std(pre_similarities)),
            '聚类后中心相似度标准差': float(np.std(post_similarities)),
            '相似度提升标准差': float(np.std(improvements))
        }
        
    def save_analysis_results(self, filename: str = None):
        """保存分析结果到JSON文件"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"clustering_similarity_comparison_{timestamp}.json"
            
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
            
        logger.info(f"分析结果已保存到: {filename}")
        return filename


def main():
    """主函数"""
    print("=" * 80)
    print("聚类前后相似度对比分析")
    print("=" * 80)
    
    try:
        # 创建分析器
        comparator = ClusteringSimilarityComparator()
        
        # 运行分析
        print("\n开始聚类前后相似度对比分析...")
        results = comparator.analyze_clustering_similarity_impact(num_cases=2000)
        
        if not results:
            print("分析失败，请检查数据路径和配置")
            return False
            
        # 保存结果
        json_file = comparator.save_analysis_results()
        
        # 显示关键结果
        print("\n" + "=" * 80)
        print("聚类前后相似度对比分析结果")
        print("=" * 80)
        
        overall_stats = results['overall_statistics']
        
        print(f"\n总体统计:")
        print(f"  - 平均聚类前相似度: {overall_stats['平均聚类前相似度']:.4f}")
        print(f"  - 平均聚类后中心相似度: {overall_stats['平均聚类后中心相似度']:.4f}")
        print(f"  - 平均聚类内部相似度: {overall_stats['平均聚类内部相似度']:.4f}")
        print(f"  - 平均聚类间相似度: {overall_stats['平均聚类间相似度']:.4f}")
        print(f"  - 平均相似度提升: {overall_stats['平均相似度提升']:.4f}")
        print(f"  - 平均聚类效果评分: {overall_stats['平均聚类效果评分']:.4f}")
        
        # 显示部分案由详细结果
        print(f"\n案由详细分析 (前5个):")
        case_type_analysis = results['case_type_analysis']
        for i, (case_type, data) in enumerate(list(case_type_analysis.items())[:5]):
            print(f"\n  {i+1}. {case_type}:")
            print(f"     案例数量: {data['案例数量']}")
            print(f"     聚类前相似度: {data['聚类前平均相似度']:.4f}")
            print(f"     聚类后中心相似度: {data['聚类后中心相似度']:.4f}")
            print(f"     聚类内部相似度: {data['聚类内部平均相似度']:.4f}")
            print(f"     聚类间相似度: {data['聚类间平均相似度']:.4f}")
            print(f"     相似度提升: {data['相似度提升']:.4f}")
            print(f"     聚类效果评分: {data['聚类效果评分']:.4f}")
        
        print(f"\n详细分析结果已保存到: {json_file}")
        
        print("\n" + "=" * 80)
        print("✓ 聚类前后相似度对比分析完成！")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"\n✗ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
