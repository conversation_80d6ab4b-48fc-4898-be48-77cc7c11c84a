#!/usr/bin/env python3
"""
模块三性能对比测试运行脚本
比较优化方案与传统方案的性能差异
"""

import sys
import os
import logging
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

from tests.test_module3_comparison import Module3ComparisonTest

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'module3_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


def run_quick_test():
    """运行快速测试（小数据集）"""
    logger.info("开始运行快速测试")
    
    test = Module3ComparisonTest()
    
    # 设置小规模测试配置
    test_configs = [
        {'provisions': 500, 'cases': 2000, 'queries': 50}
    ]
    
    # 修改测试配置
    original_method = test.run_comprehensive_comparison
    
    def quick_test():
        logger.info("运行快速对比测试")
        
        results = {
            'accuracy_comparison': {},
            'speed_comparison': {},
            'storage_comparison': {},
            'scalability_analysis': {}
        }
        
        for config in test_configs:
            config_name = f"{config['provisions']}p_{config['cases']}c"
            logger.info(f"测试配置: {config_name}")
            
            # 生成测试数据
            from src.performance_benchmark import DataGenerator
            provisions = DataGenerator.generate_legal_provisions(config['provisions'])
            case_data = DataGenerator.generate_case_nodes(config['cases'])
            queries = DataGenerator.generate_test_queries(config['queries'])
            
            # 转换案例数据
            cases = test._convert_case_data(case_data)
            
            # 1. 准确率对比
            accuracy_result = test._compare_accuracy(provisions, cases, queries)
            results['accuracy_comparison'][config_name] = accuracy_result
            
            # 2. 速度对比
            speed_result = test._compare_speed(provisions, cases, queries)
            results['speed_comparison'][config_name] = speed_result
            
            # 3. 存储占用对比
            storage_result = test._compare_storage(provisions, cases)
            results['storage_comparison'][config_name] = storage_result
            
        test.test_results = results
        return results
    
    # 运行测试
    results = quick_test()
    
    # 生成报告
    report = test.generate_comparison_report()
    print("\n" + "="*80)
    print("测试报告")
    print("="*80)
    print(report)
    
    # 保存结果
    test.save_results("quick_test_results.json")
    
    return results


def run_full_test():
    """运行完整测试（大数据集）"""
    logger.info("开始运行完整测试")
    
    test = Module3ComparisonTest()
    
    # 设置ES连接（可选）
    test.setup_elasticsearch()
    
    # 运行综合对比测试
    results = test.run_comprehensive_comparison()
    
    # 生成报告
    report = test.generate_comparison_report()
    print("\n" + "="*80)
    print("完整测试报告")
    print("="*80)
    print(report)
    
    # 保存结果
    test.save_results("full_test_results.json")
    
    # 绘制图表
    try:
        test.plot_performance_charts()
    except Exception as e:
        logger.warning(f"绘制图表失败: {e}")
    
    return results


def print_test_summary(results):
    """打印测试摘要"""
    print("\n" + "="*60)
    print("测试摘要")
    print("="*60)
    
    # 准确率摘要
    if 'accuracy_comparison' in results:
        print("\n📊 准确率对比:")
        for config, data in results['accuracy_comparison'].items():
            opt_f1 = data['optimized']['f1']
            direct_f1 = data['direct']['f1']
            improvement = data['improvement']['f1']
            print(f"  {config}: 优化方案F1={opt_f1:.3f}, 传统方案F1={direct_f1:.3f}, 提升={improvement:.1f}%")
    
    # 速度摘要
    if 'speed_comparison' in results:
        print("\n⚡ 速度对比:")
        for config, data in results['speed_comparison'].items():
            opt_time = data['query']['optimized'] * 1000
            direct_time = data['query']['direct'] * 1000
            improvement = data['query']['improvement']
            print(f"  {config}: 优化方案={opt_time:.2f}ms, 传统方案={direct_time:.2f}ms, 提升={improvement:.1f}%")
    
    # 存储摘要
    if 'storage_comparison' in results:
        print("\n💾 存储对比:")
        for config, data in results['storage_comparison'].items():
            opt_storage = data['optimized']['total_mb']
            direct_storage = data['direct']['total_mb']
            improvement = data['improvement']
            print(f"  {config}: 优化方案={opt_storage:.1f}MB, 传统方案={direct_storage:.1f}MB, 变化={improvement:.1f}%")


def main():
    """主函数"""
    print("模块三性能对比测试")
    print("="*50)
    print("1. 快速测试（小数据集，约5分钟）")
    print("2. 完整测试（大数据集，约30分钟）")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择测试类型 (1/2/3): ").strip()
        
        if choice == '1':
            try:
                results = run_quick_test()
                print_test_summary(results)
                break
            except Exception as e:
                logger.error(f"快速测试失败: {e}")
                import traceback
                traceback.print_exc()
                
        elif choice == '2':
            try:
                results = run_full_test()
                print_test_summary(results)
                break
            except Exception as e:
                logger.error(f"完整测试失败: {e}")
                import traceback
                traceback.print_exc()
                
        elif choice == '3':
            print("退出测试")
            break
            
        else:
            print("无效选择，请输入 1、2 或 3")


if __name__ == "__main__":
    main()
