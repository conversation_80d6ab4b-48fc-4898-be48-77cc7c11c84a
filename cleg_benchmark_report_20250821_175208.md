# CLEG数据基准测试报告
生成时间: 2025-08-21 17:52:08
测试配置: 100 案例, 200 法条, 50 查询

## 检索速度对比

| 检索方法 | 初始化时间(s) | 平均查询时间(s) | QPS | 内存使用(MB) |
|----------|---------------|-----------------|-----|-------------|
| 混合检索 | 0.070 | 0.013 | 76.3 | 0.0 |
| 传统检索 | 0.000 | 0.050 | 19.9 | 0.0 |
| Elasticsearch | 19.977 | 0.028 | 35.2 | 0.0 |

## 检索准确率对比

| 检索方法 | 精确率 | 召回率 | F1分数 |
|----------|--------|--------|--------|
| 混合检索 | 0.014 | 0.031 | 0.019 |
| 传统检索 | 0.018 | 0.059 | 0.027 |
| Elasticsearch | 0.024 | 0.077 | 0.036 |

## 性能提升分析

### 检索速度提升
- 相比传统检索: 73.9%
- 相比Elasticsearch: 53.8%

### 内存使用优化
- 相比传统检索: 0.0%
- 相比Elasticsearch: 0.0%

### 检索准确率提升
- F1分数相比传统检索: -28.6%
- F1分数相比Elasticsearch: -46.0%

