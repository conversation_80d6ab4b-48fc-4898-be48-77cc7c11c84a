"""
模块三性能对比测试
比较优化方案与传统方案在准确率、ES存储占用和速度方面的差异
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import time
import json
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from typing import List, Dict, Tuple
import logging
from datetime import datetime
from elasticsearch import Elasticsearch
from elasticsearch.exceptions import ConnectionError

# 导入模块
from src.data_structures import LegalResearchInput, LegalProvision, CaseNode, LegalClaimNode
from src.legal_research_module import LegalResearchModule
from src.performance_benchmark import PerformanceBenchmark, DataGenerator, TraditionalRetriever
from src.elasticsearch_hybrid_retriever import ElasticsearchHybridRetriever

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DirectCaseRetriever:
    """直接在案由中检索所有案例的传统方案"""
    
    def __init__(self):
        self.cases_by_type = {}  # {case_type: [CaseNode]}
        
    def initialize(self, cases: List[CaseNode]):
        """初始化案例数据"""
        self.cases_by_type = {}
        for case in cases:
            if case.case_type not in self.cases_by_type:
                self.cases_by_type[case.case_type] = []
            self.cases_by_type[case.case_type].append(case)
            
    def retrieve_cases(self, evidence_list: List[str], case_type: str, top_k: int = 10) -> List[Tuple[CaseNode, float]]:
        """直接在指定案由中检索所有案例"""
        if case_type not in self.cases_by_type:
            return []
            
        evidence_set = set(evidence_list)
        scores = []
        
        # 遍历该案由下的所有案例
        for case in self.cases_by_type[case_type]:
            case_evidence_set = set(case.key_evidence_list)
            
            # 计算Jaccard相似度
            intersection = len(evidence_set.intersection(case_evidence_set))
            union = len(evidence_set.union(case_evidence_set))
            similarity = intersection / union if union > 0 else 0
            
            scores.append((case, similarity))
            
        # 排序并返回Top-K
        scores.sort(key=lambda x: x[1], reverse=True)
        return scores[:top_k]


class Module3ComparisonTest:
    """模块三对比测试"""
    
    def __init__(self):
        self.test_results = {}
        self.es_client = None
        
    def setup_elasticsearch(self):
        """设置Elasticsearch连接"""
        try:
            self.es_client = Elasticsearch([{'host': 'localhost', 'port': 9200}])
            # 测试连接
            self.es_client.ping()
            logger.info("Elasticsearch连接成功")
            return True
        except ConnectionError:
            logger.warning("Elasticsearch连接失败，将跳过ES相关测试")
            return False
            
    def run_comprehensive_comparison(self):
        """运行综合对比测试"""
        logger.info("开始运行模块三综合对比测试")
        
        # 测试配置
        test_configs = [
            {'provisions': 1000, 'cases': 5000, 'queries': 100},
            {'provisions': 2000, 'cases': 10000, 'queries': 100},
            {'provisions': 5000, 'cases': 20000, 'queries': 100}
        ]
        
        results = {
            'accuracy_comparison': {},
            'speed_comparison': {},
            'storage_comparison': {},
            'scalability_analysis': {}
        }
        
        for config in test_configs:
            config_name = f"{config['provisions']}p_{config['cases']}c"
            logger.info(f"测试配置: {config_name}")
            
            # 生成测试数据
            provisions = DataGenerator.generate_legal_provisions(config['provisions'])
            case_data = DataGenerator.generate_case_nodes(config['cases'])
            queries = DataGenerator.generate_test_queries(config['queries'])
            
            # 转换案例数据
            cases = self._convert_case_data(case_data)
            
            # 1. 准确率对比
            accuracy_result = self._compare_accuracy(provisions, cases, queries)
            results['accuracy_comparison'][config_name] = accuracy_result
            
            # 2. 速度对比
            speed_result = self._compare_speed(provisions, cases, queries)
            results['speed_comparison'][config_name] = speed_result
            
            # 3. 存储占用对比
            storage_result = self._compare_storage(provisions, cases)
            results['storage_comparison'][config_name] = storage_result
            
        # 4. 可扩展性分析
        results['scalability_analysis'] = self._analyze_scalability(test_configs)
        
        self.test_results = results
        return results
        
    def _convert_case_data(self, case_data: List[Dict]) -> List[CaseNode]:
        """转换案例数据格式"""
        cases = []
        for data in case_data:
            case = CaseNode()
            case.case_id = data['case_id']
            case.case_name = data['case_name']
            case.case_type = data['case_type']
            case.court_level = data['court_level']
            case.judgment_date = data['judgment_date']
            case.case_facts_summary = data['case_facts_summary']
            case.court_opinion = data['court_opinion']
            case.judgment_result = data['judgment_result']
            case.legal_basis = data['legal_basis']
            case.key_evidence_list = data['key_evidence_list']
            case.effectiveness_status = "valid"
            cases.append(case)
        return cases
        
    def _compare_accuracy(self, provisions: List[LegalProvision], 
                         cases: List[CaseNode], queries: List[LegalResearchInput]) -> Dict:
        """对比准确率"""
        logger.info("开始准确率对比测试")
        
        # 初始化优化方案（聚类预筛选）
        optimized_module = LegalResearchModule()
        optimized_module.initialize(provisions, [case.__dict__ for case in cases])
        
        # 初始化传统方案（直接检索）
        direct_retriever = DirectCaseRetriever()
        direct_retriever.initialize(cases)
        
        optimized_scores = []
        direct_scores = []
        
        for query in queries[:50]:  # 使用前50个查询进行准确率测试
            # 生成标准答案（基于证据重合度的人工标注）
            ground_truth = self._generate_ground_truth(query, cases)
            
            if not ground_truth:
                continue
                
            # 优化方案结果
            opt_results = optimized_module.case_recommender.recommend_similar_cases(query, top_k=10)
            opt_retrieved = set([case.case_id for case, score in opt_results])
            
            # 传统方案结果
            direct_results = direct_retriever.retrieve_cases(
                query.core_evidence_list, query.case_type, top_k=10
            )
            direct_retrieved = set([case.case_id for case, score in direct_results])
            
            # 计算指标
            relevant_ids = set(ground_truth)
            
            # 优化方案指标
            opt_precision = len(opt_retrieved.intersection(relevant_ids)) / len(opt_retrieved) if opt_retrieved else 0
            opt_recall = len(opt_retrieved.intersection(relevant_ids)) / len(relevant_ids) if relevant_ids else 0
            opt_f1 = 2 * opt_precision * opt_recall / (opt_precision + opt_recall) if (opt_precision + opt_recall) > 0 else 0
            
            # 传统方案指标
            direct_precision = len(direct_retrieved.intersection(relevant_ids)) / len(direct_retrieved) if direct_retrieved else 0
            direct_recall = len(direct_retrieved.intersection(relevant_ids)) / len(relevant_ids) if relevant_ids else 0
            direct_f1 = 2 * direct_precision * direct_recall / (direct_precision + direct_recall) if (direct_precision + direct_recall) > 0 else 0
            
            optimized_scores.append({'precision': opt_precision, 'recall': opt_recall, 'f1': opt_f1})
            direct_scores.append({'precision': direct_precision, 'recall': direct_recall, 'f1': direct_f1})
            
        # 计算平均值
        opt_avg = {
            'precision': np.mean([s['precision'] for s in optimized_scores]),
            'recall': np.mean([s['recall'] for s in optimized_scores]),
            'f1': np.mean([s['f1'] for s in optimized_scores])
        }
        
        direct_avg = {
            'precision': np.mean([s['precision'] for s in direct_scores]),
            'recall': np.mean([s['recall'] for s in direct_scores]),
            'f1': np.mean([s['f1'] for s in direct_scores])
        }
        
        return {
            'optimized': opt_avg,
            'direct': direct_avg,
            'improvement': {
                'precision': (opt_avg['precision'] - direct_avg['precision']) / direct_avg['precision'] * 100 if direct_avg['precision'] > 0 else 0,
                'recall': (opt_avg['recall'] - direct_avg['recall']) / direct_avg['recall'] * 100 if direct_avg['recall'] > 0 else 0,
                'f1': (opt_avg['f1'] - direct_avg['f1']) / direct_avg['f1'] * 100 if direct_avg['f1'] > 0 else 0
            }
        }
        
    def _generate_ground_truth(self, query: LegalResearchInput, cases: List[CaseNode]) -> List[str]:
        """生成标准答案（基于证据重合度）"""
        evidence_set = set(query.core_evidence_list)
        relevant_cases = []
        
        for case in cases:
            if case.case_type != query.case_type:
                continue
                
            case_evidence_set = set(case.key_evidence_list)
            intersection = len(evidence_set.intersection(case_evidence_set))
            union = len(evidence_set.union(case_evidence_set))
            
            # 相似度阈值设为0.3，超过此阈值认为相关
            if union > 0 and intersection / union >= 0.3:
                relevant_cases.append(case.case_id)
                
        return relevant_cases[:20]  # 返回最多20个相关案例
        
    def _compare_speed(self, provisions: List[LegalProvision], 
                      cases: List[CaseNode], queries: List[LegalResearchInput]) -> Dict:
        """对比检索速度"""
        logger.info("开始速度对比测试")
        
        # 初始化优化方案
        opt_start = time.time()
        optimized_module = LegalResearchModule()
        optimized_module.initialize(provisions, [case.__dict__ for case in cases])
        opt_init_time = time.time() - opt_start
        
        # 初始化传统方案
        direct_start = time.time()
        direct_retriever = DirectCaseRetriever()
        direct_retriever.initialize(cases)
        direct_init_time = time.time() - direct_start
        
        # 测试查询速度
        opt_times = []
        direct_times = []
        
        for query in queries:
            # 优化方案查询时间
            start = time.time()
            opt_results = optimized_module.case_recommender.recommend_similar_cases(query)
            opt_times.append(time.time() - start)
            
            # 传统方案查询时间
            start = time.time()
            direct_results = direct_retriever.retrieve_cases(
                query.core_evidence_list, query.case_type
            )
            direct_times.append(time.time() - start)
            
        return {
            'initialization': {
                'optimized': opt_init_time,
                'direct': direct_init_time,
                'improvement': (direct_init_time - opt_init_time) / direct_init_time * 100 if direct_init_time > 0 else 0
            },
            'query': {
                'optimized': np.mean(opt_times),
                'direct': np.mean(direct_times),
                'improvement': (np.mean(direct_times) - np.mean(opt_times)) / np.mean(direct_times) * 100 if np.mean(direct_times) > 0 else 0
            }
        }

    def _compare_storage(self, provisions: List[LegalProvision], cases: List[CaseNode]) -> Dict:
        """对比存储占用"""
        logger.info("开始存储占用对比测试")

        # 计算优化方案存储占用
        opt_storage = self._calculate_optimized_storage(provisions, cases)

        # 计算传统方案存储占用
        direct_storage = self._calculate_direct_storage(provisions, cases)

        # ES存储占用（如果可用）
        es_storage = None
        if self.es_client:
            es_storage = self._calculate_es_storage(provisions)

        result = {
            'optimized': opt_storage,
            'direct': direct_storage,
            'improvement': (direct_storage - opt_storage) / direct_storage * 100 if direct_storage > 0 else 0
        }

        if es_storage:
            result['elasticsearch'] = es_storage

        return result

    def _calculate_optimized_storage(self, provisions: List[LegalProvision], cases: List[CaseNode]) -> Dict:
        """计算优化方案的存储占用"""
        # 法条向量存储
        provision_vectors = len(provisions) * 384 * 4  # 384维float32向量

        # 案例向量存储
        case_vectors = len(cases) * 384 * 4

        # 聚类中心存储（假设每个案由10个聚类中心）
        case_types = len(set(case.case_type for case in cases))
        cluster_centers = case_types * 10 * 384 * 4

        # 索引结构存储（估算）
        index_overhead = (len(provisions) + len(cases)) * 100  # 每个条目100字节索引开销

        # 原始数据存储
        provision_text = sum(len(p.content.encode('utf-8')) for p in provisions)
        case_text = sum(len(c.case_facts_summary.encode('utf-8')) for c in cases)

        total_bytes = provision_vectors + case_vectors + cluster_centers + index_overhead + provision_text + case_text

        return {
            'total_mb': total_bytes / 1024 / 1024,
            'vectors_mb': (provision_vectors + case_vectors + cluster_centers) / 1024 / 1024,
            'text_mb': (provision_text + case_text) / 1024 / 1024,
            'index_mb': index_overhead / 1024 / 1024
        }

    def _calculate_direct_storage(self, provisions: List[LegalProvision], cases: List[CaseNode]) -> Dict:
        """计算传统方案的存储占用"""
        # 原始数据存储
        provision_text = sum(len(p.content.encode('utf-8')) for p in provisions)
        case_text = sum(len(c.case_facts_summary.encode('utf-8')) for c in cases)

        # 简单索引存储（关键词索引）
        index_overhead = (len(provisions) + len(cases)) * 50  # 每个条目50字节索引开销

        total_bytes = provision_text + case_text + index_overhead

        return {
            'total_mb': total_bytes / 1024 / 1024,
            'vectors_mb': 0,  # 传统方案不使用向量
            'text_mb': (provision_text + case_text) / 1024 / 1024,
            'index_mb': index_overhead / 1024 / 1024
        }

    def _calculate_es_storage(self, provisions: List[LegalProvision]) -> Dict:
        """计算Elasticsearch存储占用"""
        try:
            # 创建临时ES检索器进行测试
            es_retriever = ElasticsearchHybridRetriever(self.es_client)
            es_retriever.initialize(provisions)

            # 获取索引统计信息
            stats = self.es_client.indices.stats(index=es_retriever.index_name)
            index_size = stats['indices'][es_retriever.index_name]['total']['store']['size_in_bytes']

            # 清理测试索引
            es_retriever.cleanup()

            return {
                'total_mb': index_size / 1024 / 1024,
                'note': 'ES存储包含倒排索引和向量索引'
            }
        except Exception as e:
            logger.warning(f"ES存储计算失败: {e}")
            return {'error': str(e)}

    def _analyze_scalability(self, test_configs: List[Dict]) -> Dict:
        """分析可扩展性"""
        logger.info("开始可扩展性分析")

        scalability_data = {
            'data_sizes': [],
            'optimized_times': [],
            'direct_times': [],
            'optimized_memory': [],
            'direct_memory': []
        }

        for config in test_configs:
            data_size = config['provisions'] + config['cases']
            scalability_data['data_sizes'].append(data_size)

            # 从之前的测试结果中获取数据
            config_name = f"{config['provisions']}p_{config['cases']}c"
            if config_name in self.test_results.get('speed_comparison', {}):
                speed_data = self.test_results['speed_comparison'][config_name]
                scalability_data['optimized_times'].append(speed_data['query']['optimized'])
                scalability_data['direct_times'].append(speed_data['query']['direct'])

            if config_name in self.test_results.get('storage_comparison', {}):
                storage_data = self.test_results['storage_comparison'][config_name]
                scalability_data['optimized_memory'].append(storage_data['optimized']['total_mb'])
                scalability_data['direct_memory'].append(storage_data['direct']['total_mb'])

        return scalability_data

    def generate_comparison_report(self) -> str:
        """生成对比报告"""
        if not self.test_results:
            return "没有测试结果"

        report = ["# 模块三方案对比测试报告\n\n"]

        # 准确率对比
        report.append("## 1. 准确率对比\n\n")
        report.append("| 配置 | 方案 | 精确率 | 召回率 | F1分数 |\n")
        report.append("|------|------|--------|--------|--------|\n")

        for config, data in self.test_results.get('accuracy_comparison', {}).items():
            opt = data['optimized']
            direct = data['direct']
            report.append(f"| {config} | 优化方案 | {opt['precision']:.3f} | {opt['recall']:.3f} | {opt['f1']:.3f} |\n")
            report.append(f"| {config} | 传统方案 | {direct['precision']:.3f} | {direct['recall']:.3f} | {direct['f1']:.3f} |\n")

        # 速度对比
        report.append("\n## 2. 速度对比\n\n")
        report.append("| 配置 | 方案 | 初始化时间(s) | 查询时间(ms) |\n")
        report.append("|------|------|---------------|-------------|\n")

        for config, data in self.test_results.get('speed_comparison', {}).items():
            opt_init = data['initialization']['optimized']
            direct_init = data['initialization']['direct']
            opt_query = data['query']['optimized'] * 1000
            direct_query = data['query']['direct'] * 1000

            report.append(f"| {config} | 优化方案 | {opt_init:.2f} | {opt_query:.2f} |\n")
            report.append(f"| {config} | 传统方案 | {direct_init:.2f} | {direct_query:.2f} |\n")

        # 存储对比
        report.append("\n## 3. 存储占用对比\n\n")
        report.append("| 配置 | 方案 | 总存储(MB) | 向量存储(MB) | 文本存储(MB) |\n")
        report.append("|------|------|------------|--------------|-------------|\n")

        for config, data in self.test_results.get('storage_comparison', {}).items():
            opt = data['optimized']
            direct = data['direct']

            report.append(f"| {config} | 优化方案 | {opt['total_mb']:.1f} | {opt['vectors_mb']:.1f} | {opt['text_mb']:.1f} |\n")
            report.append(f"| {config} | 传统方案 | {direct['total_mb']:.1f} | {direct['vectors_mb']:.1f} | {direct['text_mb']:.1f} |\n")

        # 性能提升总结
        report.append("\n## 4. 性能提升总结\n\n")

        # 计算平均提升
        accuracy_improvements = []
        speed_improvements = []
        storage_improvements = []

        for config, data in self.test_results.get('accuracy_comparison', {}).items():
            accuracy_improvements.append(data['improvement']['f1'])

        for config, data in self.test_results.get('speed_comparison', {}).items():
            speed_improvements.append(data['query']['improvement'])

        for config, data in self.test_results.get('storage_comparison', {}).items():
            storage_improvements.append(data['improvement'])

        if accuracy_improvements:
            avg_accuracy_improvement = np.mean(accuracy_improvements)
            report.append(f"- **准确率提升**: 平均F1分数提升 {avg_accuracy_improvement:.1f}%\n")

        if speed_improvements:
            avg_speed_improvement = np.mean(speed_improvements)
            report.append(f"- **查询速度提升**: 平均查询时间减少 {avg_speed_improvement:.1f}%\n")

        if storage_improvements:
            avg_storage_improvement = np.mean(storage_improvements)
            if avg_storage_improvement > 0:
                report.append(f"- **存储优化**: 平均存储占用减少 {avg_storage_improvement:.1f}%\n")
            else:
                report.append(f"- **存储开销**: 平均存储占用增加 {abs(avg_storage_improvement):.1f}%（换取性能提升）\n")

        return "".join(report)

    def save_results(self, filename: str = None):
        """保存测试结果"""
        if filename is None:
            filename = f"module3_comparison_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2, default=str)

        logger.info(f"测试结果已保存到: {filename}")

    def plot_performance_charts(self):
        """绘制性能对比图表"""
        if not self.test_results:
            logger.warning("没有测试结果可绘制")
            return

        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('模块三方案性能对比', fontsize=16)

        # 准确率对比
        self._plot_accuracy_comparison(axes[0, 0])

        # 速度对比
        self._plot_speed_comparison(axes[0, 1])

        # 存储对比
        self._plot_storage_comparison(axes[1, 0])

        # 可扩展性分析
        self._plot_scalability_analysis(axes[1, 1])

        plt.tight_layout()
        plt.savefig(f'module3_performance_comparison_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png',
                   dpi=300, bbox_inches='tight')
        plt.show()

    def _plot_accuracy_comparison(self, ax):
        """绘制准确率对比图"""
        configs = list(self.test_results.get('accuracy_comparison', {}).keys())
        opt_f1 = [data['optimized']['f1'] for data in self.test_results['accuracy_comparison'].values()]
        direct_f1 = [data['direct']['f1'] for data in self.test_results['accuracy_comparison'].values()]

        x = np.arange(len(configs))
        width = 0.35

        ax.bar(x - width/2, opt_f1, width, label='优化方案', alpha=0.8)
        ax.bar(x + width/2, direct_f1, width, label='传统方案', alpha=0.8)

        ax.set_xlabel('测试配置')
        ax.set_ylabel('F1分数')
        ax.set_title('准确率对比')
        ax.set_xticks(x)
        ax.set_xticklabels(configs, rotation=45)
        ax.legend()
        ax.grid(True, alpha=0.3)

    def _plot_speed_comparison(self, ax):
        """绘制速度对比图"""
        configs = list(self.test_results.get('speed_comparison', {}).keys())
        opt_times = [data['query']['optimized'] * 1000 for data in self.test_results['speed_comparison'].values()]
        direct_times = [data['query']['direct'] * 1000 for data in self.test_results['speed_comparison'].values()]

        x = np.arange(len(configs))
        width = 0.35

        ax.bar(x - width/2, opt_times, width, label='优化方案', alpha=0.8)
        ax.bar(x + width/2, direct_times, width, label='传统方案', alpha=0.8)

        ax.set_xlabel('测试配置')
        ax.set_ylabel('查询时间(ms)')
        ax.set_title('查询速度对比')
        ax.set_xticks(x)
        ax.set_xticklabels(configs, rotation=45)
        ax.legend()
        ax.grid(True, alpha=0.3)

    def _plot_storage_comparison(self, ax):
        """绘制存储对比图"""
        configs = list(self.test_results.get('storage_comparison', {}).keys())
        opt_storage = [data['optimized']['total_mb'] for data in self.test_results['storage_comparison'].values()]
        direct_storage = [data['direct']['total_mb'] for data in self.test_results['storage_comparison'].values()]

        x = np.arange(len(configs))
        width = 0.35

        ax.bar(x - width/2, opt_storage, width, label='优化方案', alpha=0.8)
        ax.bar(x + width/2, direct_storage, width, label='传统方案', alpha=0.8)

        ax.set_xlabel('测试配置')
        ax.set_ylabel('存储占用(MB)')
        ax.set_title('存储占用对比')
        ax.set_xticks(x)
        ax.set_xticklabels(configs, rotation=45)
        ax.legend()
        ax.grid(True, alpha=0.3)

    def _plot_scalability_analysis(self, ax):
        """绘制可扩展性分析图"""
        scalability = self.test_results.get('scalability_analysis', {})
        if not scalability.get('data_sizes'):
            ax.text(0.5, 0.5, '无可扩展性数据', ha='center', va='center', transform=ax.transAxes)
            return

        data_sizes = scalability['data_sizes']
        opt_times = [t * 1000 for t in scalability.get('optimized_times', [])]
        direct_times = [t * 1000 for t in scalability.get('direct_times', [])]

        if opt_times and direct_times:
            ax.plot(data_sizes, opt_times, 'o-', label='优化方案', linewidth=2)
            ax.plot(data_sizes, direct_times, 's-', label='传统方案', linewidth=2)

            ax.set_xlabel('数据规模')
            ax.set_ylabel('查询时间(ms)')
            ax.set_title('可扩展性分析')
            ax.legend()
            ax.grid(True, alpha=0.3)


def main():
    """主测试函数"""
    test = Module3ComparisonTest()

    # 设置ES连接（可选）
    test.setup_elasticsearch()

    # 运行综合对比测试
    results = test.run_comprehensive_comparison()

    # 生成报告
    report = test.generate_comparison_report()
    print(report)

    # 保存结果
    test.save_results()

    # 绘制图表
    test.plot_performance_charts()

    return results


if __name__ == "__main__":
    main()
