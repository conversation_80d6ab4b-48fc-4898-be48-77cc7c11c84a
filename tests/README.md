# 模块三性能对比测试

本测试套件用于比较模块三的优化方案（聚类预筛选 + 混合检索）与传统方案（直接在案由中检索所有案例）在准确率、ES存储占用和速度方面的差异。

## 测试方案对比

### 优化方案（你的方案）
- **聚类预筛选**: 对高频案由使用证据向量聚类，快速定位相关案例簇
- **混合检索**: 结合语义向量检索和BM25关键词检索
- **覆盖关系处理**: 智能过滤被覆盖的陈旧案例
- **多维度相似度**: 综合证据、事实、法律争议的相似度计算

### 传统方案（对比基准）
- **直接检索**: 在指定案由中遍历所有案例
- **简单相似度**: 仅使用Jaccard相似度计算证据重合度
- **无预筛选**: 每次查询都需要遍历全部案例
- **无覆盖处理**: 不区分案例的时效性和权威性

## 测试指标

### 1. 准确率指标
- **精确率 (Precision)**: 检索结果中相关案例的比例
- **召回率 (Recall)**: 相关案例中被检索到的比例  
- **F1分数**: 精确率和召回率的调和平均数

### 2. 速度指标
- **初始化时间**: 系统启动和索引构建时间
- **查询时间**: 单次查询的平均响应时间
- **可扩展性**: 随数据规模增长的性能变化

### 3. 存储指标
- **总存储占用**: 包括索引、向量、原始数据的总存储空间
- **向量存储**: 语义向量和聚类中心的存储开销
- **索引存储**: 检索索引结构的存储开销
- **ES存储**: Elasticsearch混合索引的存储占用（可选）

## 快速开始

### 环境要求
```bash
pip install numpy pandas matplotlib elasticsearch jieba scikit-learn psutil
```

### 运行测试
```bash
# 方式1: 使用便捷脚本（推荐）
python run_module3_test.py

# 方式2: 直接运行测试
python tests/test_module3_comparison.py
```

### 测试选项
1. **快速测试**: 小数据集（500法条，2000案例），约5分钟
2. **完整测试**: 大数据集（5000法条，20000案例），约30分钟

## 测试结果

### 预期性能优势

根据模块三文档的设计，优化方案预期具有以下优势：

1. **准确率提升 15-25%**
   - 语义检索捕捉深层含义
   - 多维度相似度计算更精准
   - 覆盖关系处理提升结果质量

2. **查询速度提升 60-80%**
   - 聚类预筛选减少候选集
   - 向量索引加速相似度计算
   - 智能缓存机制

3. **存储开销增加 20-40%**
   - 向量存储需要额外空间
   - 聚类中心和索引结构
   - 换取查询性能的提升

### 报告格式

测试完成后会生成：

1. **控制台报告**: 实时显示测试进度和结果摘要
2. **详细报告**: Markdown格式的完整性能对比报告
3. **JSON结果**: 机器可读的详细测试数据
4. **性能图表**: 可视化的性能对比图表（PNG格式）

## 测试数据

### 模拟数据生成
- **法条数据**: 包含民法典、刑法等主要法律的模拟条款
- **案例数据**: 涵盖民事、刑事、行政等不同案由的模拟案例
- **证据类型**: 合同文本、转账记录、聊天记录等常见证据
- **查询数据**: 基于真实案件特征的模拟查询

### 标准答案生成
使用基于证据重合度的自动标注方法：
- 计算查询证据与案例证据的Jaccard相似度
- 设置0.3的相似度阈值判定相关性
- 确保标准答案的客观性和一致性

## Elasticsearch集成（可选）

如果本地运行了Elasticsearch服务，测试会自动包含ES混合检索的对比：

```bash
# 启动Elasticsearch（Docker）
docker run -d --name elasticsearch \
  -p 9200:9200 -p 9300:9300 \
  -e "discovery.type=single-node" \
  elasticsearch:8.8.0
```

ES测试包括：
- 原生向量搜索（ES 8.0+）
- 混合查询（文本+向量）
- 存储占用分析

## 故障排除

### 常见问题

1. **内存不足**
   ```
   解决方案: 减少测试数据规模或增加系统内存
   ```

2. **依赖包缺失**
   ```bash
   pip install -r requirements.txt
   ```

3. **Elasticsearch连接失败**
   ```
   跳过ES测试，不影响主要对比结果
   ```

4. **图表显示问题**
   ```bash
   # Linux环境可能需要
   export DISPLAY=:0
   ```

### 调试模式
```bash
# 启用详细日志
export PYTHONPATH=.
python -m pytest tests/test_module3_comparison.py -v -s
```

## 结果解读

### 性能提升指标
- **正值**: 优化方案优于传统方案
- **负值**: 传统方案在该指标上更优
- **存储开销**: 通常为负值（增加），这是性能换空间的权衡

### 可扩展性分析
观察随数据规模增长的性能曲线：
- **线性增长**: 良好的可扩展性
- **指数增长**: 存在性能瓶颈
- **优化方案应表现出更好的可扩展性**

## 贡献指南

如需扩展测试功能：

1. 添加新的测试指标到 `Module3ComparisonTest` 类
2. 实现对应的测试方法
3. 更新报告生成逻辑
4. 添加相应的可视化图表

## 许可证

本测试套件遵循项目主许可证。
